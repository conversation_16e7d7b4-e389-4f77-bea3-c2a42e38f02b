"""
统一串码验证服务
整合内部数据库查重和第三方API查询功能
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

logger = logging.getLogger(__name__)


class UnifiedSerialValidationService:
    """
    统一串码验证服务
    
    整合以下两个验证源：
    1. 内部数据库查重 (serial_number_service)
    2. 第三方API查询 (notarization_service)
    
    业务规则：串码只有在两个数据源都没有重复时才可用
    """
    
    def __init__(self):
        self.internal_service = None
        self.notarization_service = None
        self._init_services()
    
    def _init_services(self):
        """初始化依赖服务"""
        try:
            # 初始化内部串码服务
            from app.services.serial_number_service_v2 import get_serial_service
            self.internal_service = get_serial_service()
            
            # 初始化公证服务
            from app.services.notarization_service import get_notarization_service
            self.notarization_service = get_notarization_service()
            
            logger.info("统一串码验证服务初始化成功")
        except Exception as e:
            logger.error(f"统一串码验证服务初始化失败: {str(e)}")
            raise
    
    def validate_serial_format(self, serial_number: str) -> Dict[str, Any]:
        """
        验证串码格式
        
        Args:
            serial_number: 待验证的串码
            
        Returns:
            Dict: 验证结果
        """
        from app.services.serial_number_service import validate_serial_number
        return validate_serial_number(serial_number)
    
    def check_internal_database(self, serial_numbers: List[str]) -> Dict[str, Any]:
        """
        检查内部数据库中的串码重复情况
        
        Args:
            serial_numbers: 串码列表
            
        Returns:
            Dict: 检查结果
        """
        try:
            results = {
                'success': True,
                'duplicates': [],
                'available': [],
                'errors': []
            }
            
            for serial_number in serial_numbers:
                try:
                    is_duplicate = self.internal_service.check_duplicate(serial_number)
                    if is_duplicate:
                        # 获取现有记录信息
                        existing_info = self.internal_service.get_serial_number_info(serial_number)
                        results['duplicates'].append({
                            'serial_number': serial_number,
                            'source': 'internal_database',
                            'existing_record': existing_info
                        })
                    else:
                        results['available'].append(serial_number)
                except Exception as e:
                    logger.error(f"检查内部数据库串码 {serial_number} 时出错: {str(e)}")
                    results['errors'].append({
                        'serial_number': serial_number,
                        'error': str(e)
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"检查内部数据库失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'duplicates': [],
                'available': [],
                'errors': []
            }
    
    def check_third_party_api(self, serial_numbers: List[str]) -> Dict[str, Any]:
        """
        检查第三方API中的串码重复情况

        Args:
            serial_numbers: 串码列表

        Returns:
            Dict: 检查结果
        """
        try:
            # 检查是否在Flask请求上下文中
            from flask import has_request_context
            from flask_login import current_user

            if not has_request_context():
                # 在测试环境中，直接调用API工具类
                logger.warning("不在Flask请求上下文中，跳过权限检查")
                from app.services.notarization_service import NotarizationApiUtils
                api_utils = NotarizationApiUtils()

                # 验证IMEI格式
                valid_imeis = []
                invalid_imeis = []

                for imei in serial_numbers:
                    if api_utils.validate_imei(imei):
                        valid_imeis.append(imei)
                    else:
                        invalid_imeis.append(imei)

                if not valid_imeis:
                    return {
                        'success': False,
                        'error': '没有有效的IMEI编码',
                        'duplicates': [],
                        'available': [],
                        'errors': []
                    }

                # 直接调用API
                params = {'imeiCodes': valid_imeis}
                response_data = api_utils.send_request('gzs/allImeiCodeQuery', params)

                # 解析响应
                if response_data.get('success'):
                    response_json = response_data.get('response_json', {})
                    code = response_json.get('code')
                    success = response_json.get('success', False)

                    if code == 0 and success:
                        data = response_json.get('data', {})
                        duplicate_imeis = data.get('imeiCodes', [])
                        available_imeis = [imei for imei in valid_imeis if imei not in duplicate_imeis]

                        return {
                            'success': True,
                            'duplicates': [{'serial_number': imei, 'source': 'third_party_api'} for imei in duplicate_imeis],
                            'available': available_imeis,
                            'errors': []
                        }
                    else:
                        return {
                            'success': False,
                            'error': f'第三方API查询失败: {response_json.get("msg", "未知错误")}',
                            'duplicates': [],
                            'available': [],
                            'errors': []
                        }
                else:
                    return {
                        'success': False,
                        'error': response_data.get('error', '第三方API请求失败'),
                        'duplicates': [],
                        'available': [],
                        'errors': []
                    }
            else:
                # 在Flask请求上下文中，使用公证服务
                result = self.notarization_service.query_imei_validity(serial_numbers)

                if not result.get('success'):
                    return {
                        'success': False,
                        'error': result.get('error', '第三方API查询失败'),
                        'duplicates': [],
                        'available': [],
                        'errors': []
                    }

                data = result.get('data', {})
                duplicate_imeis = data.get('duplicate_imeis', [])
                available_imeis = data.get('available_imeis', [])

                return {
                    'success': True,
                    'duplicates': [{'serial_number': imei, 'source': 'third_party_api'} for imei in duplicate_imeis],
                    'available': available_imeis,
                    'errors': []
                }

        except Exception as e:
            logger.error(f"检查第三方API失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'duplicates': [],
                'available': [],
                'errors': []
            }

    def unified_validation(self, serial_numbers: List[str], check_sources: str = 'both') -> Dict[str, Any]:
        """
        统一验证串码

        Args:
            serial_numbers: 串码列表
            check_sources: 检查源 ('internal', 'api', 'both')

        Returns:
            Dict: 统一验证结果
        """
        try:
            # 1. 格式验证
            valid_serials = []
            invalid_serials = []

            for serial in serial_numbers:
                validation = self.validate_serial_format(serial)
                if validation['valid']:
                    valid_serials.append(validation['cleaned_serial'])
                else:
                    invalid_serials.append({
                        'serial_number': serial,
                        'error': validation['error']
                    })

            if not valid_serials:
                return {
                    'success': False,
                    'error': '没有有效的串码',
                    'invalid_serials': invalid_serials,
                    'results': []
                }

            # 2. 并行检查两个数据源
            internal_result = {'success': True, 'duplicates': [], 'available': [], 'errors': []}
            api_result = {'success': True, 'duplicates': [], 'available': [], 'errors': []}

            if check_sources in ['internal', 'both']:
                internal_result = self.check_internal_database(valid_serials)

            if check_sources in ['api', 'both']:
                api_result = self.check_third_party_api(valid_serials)

            # 3. 合并结果并应用业务规则
            unified_results = self._merge_validation_results(
                valid_serials, internal_result, api_result, check_sources
            )

            # 4. 自动录入可用的串码到内部数据库
            auto_insert_result = self._auto_insert_available_serials(unified_results, check_sources)

            return {
                'success': True,
                'check_sources': check_sources,
                'total_checked': len(valid_serials),
                'invalid_serials': invalid_serials,
                'results': unified_results,
                'summary': self._generate_summary(unified_results),
                'auto_insert': auto_insert_result
            }

        except Exception as e:
            logger.error(f"统一验证失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'invalid_serials': [],
                'results': []
            }

    def _merge_validation_results(self, serial_numbers: List[str],
                                internal_result: Dict[str, Any],
                                api_result: Dict[str, Any],
                                check_sources: str) -> List[Dict[str, Any]]:
        """
        合并验证结果并应用业务规则

        Args:
            serial_numbers: 串码列表
            internal_result: 内部数据库检查结果
            api_result: 第三方API检查结果
            check_sources: 检查源

        Returns:
            List: 合并后的结果列表
        """
        results = []

        # 创建查找字典
        internal_duplicates = {item['serial_number']: item for item in internal_result.get('duplicates', [])}
        api_duplicates = {item['serial_number']: item for item in api_result.get('duplicates', [])}

        for serial_number in serial_numbers:
            internal_duplicate = internal_duplicates.get(serial_number)
            api_duplicate = api_duplicates.get(serial_number)

            # 应用业务规则
            if check_sources == 'both':
                if internal_duplicate and api_duplicate:
                    status = 'blocked'
                    reason = '在内部数据库和第三方API中都发现重复'
                    conflict_sources = ['internal_database', 'third_party_api']
                elif internal_duplicate or api_duplicate:
                    status = 'partial_conflict'
                    reason = f'在{"内部数据库" if internal_duplicate else "第三方API"}中发现重复'
                    conflict_sources = ['internal_database'] if internal_duplicate else ['third_party_api']
                else:
                    status = 'available'
                    reason = '在所有数据源中都未发现重复'
                    conflict_sources = []
            elif check_sources == 'internal':
                if internal_duplicate:
                    status = 'blocked'
                    reason = '在内部数据库中发现重复'
                    conflict_sources = ['internal_database']
                else:
                    status = 'available'
                    reason = '在内部数据库中未发现重复'
                    conflict_sources = []
            elif check_sources == 'api':
                if api_duplicate:
                    status = 'blocked'
                    reason = '在第三方API中发现重复'
                    conflict_sources = ['third_party_api']
                else:
                    status = 'available'
                    reason = '在第三方API中未发现重复'
                    conflict_sources = []

            result_item = {
                'serial_number': serial_number,
                'status': status,
                'reason': reason,
                'conflict_sources': conflict_sources,
                'details': {
                    'internal_database': internal_duplicate,
                    'third_party_api': api_duplicate
                }
            }

            results.append(result_item)

        return results

    def _auto_insert_available_serials(self, unified_results: List[Dict[str, Any]],
                                     check_sources: str) -> Dict[str, Any]:
        """
        自动录入可用的串码到内部数据库

        Args:
            unified_results: 统一验证结果列表
            check_sources: 检查源类型

        Returns:
            Dict: 自动录入结果
        """
        try:
            # 只有在检查内部数据库时才进行自动录入
            if check_sources not in ['internal', 'both']:
                return {
                    'enabled': False,
                    'reason': '未检查内部数据库，跳过自动录入',
                    'total_processed': 0,
                    'successfully_added': [],
                    'already_exists': [],
                    'failed': []
                }

            # 筛选出需要自动录入的串码
            # 规则：在内部数据库中不存在重复的串码（status为'available'且内部数据库检查通过）
            serials_to_insert = []

            for result in unified_results:
                # 检查是否在内部数据库中可用
                internal_details = result.get('details', {}).get('internal_database')

                # 如果内部数据库检查结果为None（即未发现重复），则可以录入
                if internal_details is None:
                    serials_to_insert.append(result['serial_number'])

            if not serials_to_insert:
                return {
                    'enabled': True,
                    'reason': '没有需要录入的串码',
                    'total_processed': 0,
                    'successfully_added': [],
                    'already_exists': [],
                    'failed': []
                }

            # 获取当前用户信息（如果在Flask请求上下文中）
            created_by = 'system_auto_insert'
            try:
                from flask import has_request_context
                if has_request_context():
                    from flask_login import current_user
                    if current_user and current_user.is_authenticated:
                        created_by = f"{current_user.username}_auto"
            except ImportError:
                pass

            # 执行批量插入
            batch_result = self.internal_service.batch_add_serial_numbers(
                serial_numbers=serials_to_insert,
                created_by=created_by,
                notes='统一验证自动录入'
            )

            # 更新验证结果，标记哪些串码被自动录入
            for result in unified_results:
                serial_number = result['serial_number']
                if serial_number in batch_result.get('successfully_added', []):
                    result['auto_inserted'] = True
                    result['auto_insert_status'] = 'success'
                elif serial_number in batch_result.get('already_exists', []):
                    result['auto_inserted'] = False
                    result['auto_insert_status'] = 'already_exists'
                elif any(f['serial_number'] == serial_number for f in batch_result.get('failed', [])):
                    result['auto_inserted'] = False
                    result['auto_insert_status'] = 'failed'
                else:
                    result['auto_inserted'] = False
                    result['auto_insert_status'] = 'not_applicable'

            logger.info(f"自动录入完成: 处理{len(serials_to_insert)}个串码，成功{len(batch_result.get('successfully_added', []))}个")

            return {
                'enabled': True,
                'reason': '自动录入执行完成',
                'total_processed': batch_result.get('total_processed', 0),
                'successfully_added': batch_result.get('successfully_added', []),
                'already_exists': batch_result.get('already_exists', []),
                'failed': batch_result.get('failed', []),
                'message': batch_result.get('message', '')
            }

        except Exception as e:
            logger.error(f"自动录入串码时发生错误: {str(e)}")
            return {
                'enabled': True,
                'reason': f'自动录入失败: {str(e)}',
                'total_processed': 0,
                'successfully_added': [],
                'already_exists': [],
                'failed': [],
                'error': str(e)
            }

    def _generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成验证结果摘要

        Args:
            results: 验证结果列表

        Returns:
            Dict: 摘要信息
        """
        summary = {
            'total': len(results),
            'available': 0,
            'partial_conflict': 0,
            'blocked': 0
        }

        for result in results:
            status = result['status']
            if status in summary:
                summary[status] += 1

        return summary


# 全局服务实例
_unified_service_instance = None


def get_unified_serial_validation_service() -> UnifiedSerialValidationService:
    """
    获取统一串码验证服务实例（单例模式）

    Returns:
        UnifiedSerialValidationService: 服务实例
    """
    global _unified_service_instance

    if _unified_service_instance is None:
        _unified_service_instance = UnifiedSerialValidationService()

    return _unified_service_instance


def validate_serial_numbers_unified(serial_numbers: List[str],
                                   check_sources: str = 'both') -> Dict[str, Any]:
    """
    便捷函数：统一验证串码

    Args:
        serial_numbers: 串码列表
        check_sources: 检查源 ('internal', 'api', 'both')

    Returns:
        Dict: 验证结果
    """
    service = get_unified_serial_validation_service()
    return service.unified_validation(serial_numbers, check_sources)
