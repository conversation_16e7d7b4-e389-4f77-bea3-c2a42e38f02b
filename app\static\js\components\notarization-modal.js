/**
 * 公证接口模态框组件
 * 提供公证相关功能的用户界面
 */

class NotarizationModal {
    constructor() {
        this.modal = null;
        this.currentTab = 'settle-order';
        this.isLoading = false;
        this.pendingRequests = new Set(); // 防止重复请求
        this.debounceTimers = new Map(); // 防抖计时器
        this.init();
    }

    init() {
        try {
            this.createModal();
            this.bindEvents();
            this.loadUserPermissions();
        } catch (error) {
            console.error('公证模态框初始化失败:', error);
            throw error;
        }
    }

    addCustomStyles() {
        // 检查是否已经添加了样式
        if (document.getElementById('notarization-modal-styles')) {
            return;
        }

        const styles = `
            <style id="notarization-modal-styles">
                /* 统一串码验证输入增强样式 */
                #unifiedValidationCodes {
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    line-height: 1.6;
                    resize: vertical;
                    min-height: 100px;
                }

                #unifiedValidationCodes:focus {
                    border-color: #007bff;
                    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                }

                /* 验证预览样式 */
                #validationPreview {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 0.375rem;
                    padding: 0.75rem;
                }

                #validationPreviewList .badge {
                    font-size: 0.75rem;
                    padding: 0.375rem 0.5rem;
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                }

                /* 计数器样式 */
                #validationCounter {
                    font-size: 0.75rem;
                    animation: fadeIn 0.3s ease-in-out;
                }

                /* 复制按钮样式 */
                .btn-outline-success:hover {
                    transform: translateY(-1px);
                    transition: all 0.2s ease;
                }

                /* 结果卡片增强 */
                .card.border-primary {
                    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                    border-width: 2px;
                }

                .card-header.bg-primary-light {
                    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
                    border-bottom: 1px solid #2196f3;
                }

                /* 动画效果 */
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(-10px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                /* 响应式调整 */
                @media (max-width: 768px) {
                    #unifiedValidationCodes {
                        font-size: 14px;
                    }

                    #validationPreviewList .badge {
                        font-size: 0.7rem;
                        margin-bottom: 0.25rem;
                    }

                    .btn-outline-success {
                        font-size: 0.8rem;
                        padding: 0.25rem 0.5rem;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    createModal() {
        // 添加自定义样式
        this.addCustomStyles();

        const modalHtml = `
            <div class="modal fade" id="notarizationModal" tabindex="-1" aria-labelledby="notarizationModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="notarizationModalLabel">
                                <i class="bi bi-shield-check me-2"></i>公证接口工具
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- 功能选项卡 -->
                            <ul class="nav nav-tabs mb-3" id="notarizationTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="settle-order-tab" data-bs-toggle="tab" 
                                            data-bs-target="#settle-order" type="button" role="tab">
                                        <i class="bi bi-check-circle me-1"></i>结清订单
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="query-orders-tab" data-bs-toggle="tab" 
                                            data-bs-target="#query-orders" type="button" role="tab">
                                        <i class="bi bi-search me-1"></i>查询订单
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="unified-validation-tab" data-bs-toggle="tab"
                                            data-bs-target="#unified-validation" type="button" role="tab">
                                        <i class="bi bi-shield-check me-1"></i>统一串码验证
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="push-imei-tab" data-bs-toggle="tab" 
                                            data-bs-target="#push-imei" type="button" role="tab">
                                        <i class="bi bi-upload me-1"></i>推送IMEI
                                    </button>
                                </li>
                            </ul>

                            <!-- 选项卡内容 -->
                            <div class="tab-content" id="notarizationTabContent">
                                <!-- 结清订单 -->
                                <div class="tab-pane fade show active" id="settle-order" role="tabpanel">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning-light">
                                            <h6 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>结清公证订单</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-warning" role="alert">
                                                <strong>注意：</strong>结清操作不可逆，请确认订单号正确！
                                            </div>
                                            <form id="settleOrderForm">
                                                <div class="mb-3">
                                                    <label for="settleOrderId" class="form-label">订单号（三方流水号）</label>
                                                    <input type="text" class="form-control" id="settleOrderId" 
                                                           placeholder="请输入要结清的订单号" required>
                                                    <div class="form-text">请输入完整的订单号，例如：OI1970412769182351360</div>
                                                </div>
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="bi bi-check-circle me-1"></i>结清订单
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- 查询订单 -->
                                <div class="tab-pane fade" id="query-orders" role="tabpanel">
                                    <div class="card border-info">
                                        <div class="card-header bg-info-light">
                                            <h6 class="mb-0"><i class="bi bi-search me-2"></i>查询用户有效订单</h6>
                                        </div>
                                        <div class="card-body">
                                            <form id="queryOrdersForm">
                                                <div class="mb-3">
                                                    <label for="queryIdCard" class="form-label">身份证号</label>
                                                    <input type="text" class="form-control" id="queryIdCard" 
                                                           placeholder="请输入18位身份证号" maxlength="18" required>
                                                    <div class="form-text">查询用户的有效公证订单数和在租台数</div>
                                                </div>
                                                <button type="submit" class="btn btn-info">
                                                    <i class="bi bi-search me-1"></i>查询订单
                                                </button>
                                            </form>
                                            <div id="queryOrdersResult" class="mt-3" style="display: none;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 统一串码验证 -->
                                <div class="tab-pane fade" id="unified-validation" role="tabpanel">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary-light">
                                            <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>统一串码验证</h6>
                                        </div>
                                        <div class="card-body">
                                            <!-- 验证选项 -->
                                            <div class="mb-4">
                                                <label class="form-label fw-semibold">验证范围</label>
                                                <div class="btn-group w-100" role="group" aria-label="验证范围选择">
                                                    <input type="radio" class="btn-check" name="notarizationCheckSources" id="notarizationCheckBoth" value="both" checked>
                                                    <label class="btn btn-outline-primary" for="notarizationCheckBoth">
                                                        <i class="bi bi-shield-check me-1"></i>全面验证
                                                    </label>

                                                    <input type="radio" class="btn-check" name="notarizationCheckSources" id="notarizationCheckInternal" value="internal">
                                                    <label class="btn btn-outline-info" for="notarizationCheckInternal">
                                                        <i class="bi bi-database me-1"></i>内部数据库
                                                    </label>

                                                    <input type="radio" class="btn-check" name="notarizationCheckSources" id="notarizationCheckApi" value="api">
                                                    <label class="btn btn-outline-warning" for="notarizationCheckApi">
                                                        <i class="bi bi-cloud me-1"></i>第三方API
                                                    </label>
                                                </div>
                                                <div class="form-text">
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    <span id="notarizationCheckSourcesHelp">全面验证：同时检查内部数据库和第三方API，确保串码在所有数据源中都可用</span>
                                                </div>
                                            </div>

                                            <!-- 数据库统计卡片 -->
                                            <div id="serialStatisticsCard" class="mb-4">
                                                <div class="card border-info">
                                                    <div class="card-header bg-info-light">
                                                        <h6 class="mb-0">
                                                            <i class="bi bi-bar-chart me-2"></i>内部数据库统计
                                                            <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="notarizationModal.refreshStatistics()">
                                                                <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                                            </button>
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div id="statisticsContent">
                                                            <div class="text-center">
                                                                <div class="spinner-border spinner-border-sm text-info" role="status">
                                                                    <span class="visually-hidden">加载中...</span>
                                                                </div>
                                                                <small class="text-muted ms-2">正在加载统计数据...</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <form id="unifiedValidationForm">
                                                <div class="mb-3">
                                                    <label for="unifiedValidationCodes" class="form-label">
                                                        串码输入
                                                        <span id="validationCounter" class="badge bg-secondary ms-2" style="display: none;">0个</span>
                                                    </label>
                                                    <textarea class="form-control" id="unifiedValidationCodes" rows="4"
                                                              placeholder="支持多种输入格式：&#10;• 逗号分隔：354314285665078,357573129006859&#10;• 空格分隔：354314285665078 357573129006859&#10;• 每行一个：&#10;  354314285665078&#10;  357573129006859&#10;• 从Excel复制粘贴也支持" required></textarea>
                                                    <div class="form-text">
                                                        <i class="bi bi-info-circle me-1"></i>
                                                        支持多种分隔符（逗号、空格、换行），自动识别和验证15位数字串码
                                                    </div>
                                                    <div id="validationPreview" class="mt-2" style="display: none;">
                                                        <small class="text-muted">检测到的串码：</small>
                                                        <div id="validationPreviewList" class="d-flex flex-wrap gap-1 mt-1"></div>
                                                    </div>
                                                </div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-search me-1"></i>开始验证
                                                </button>
                                            </form>
                                            <div id="unifiedValidationResult" class="mt-3" style="display: none;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 推送IMEI -->
                                <div class="tab-pane fade" id="push-imei" role="tabpanel">
                                    <div class="card border-success">
                                        <div class="card-header bg-success-light">
                                            <h6 class="mb-0"><i class="bi bi-upload me-2"></i>推送设备IMEI编码</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-info" role="alert">
                                                <strong>说明：</strong>此功能在公证书状态为"视频已通过"时使用
                                            </div>
                                            <form id="pushImeiForm">
                                                <div class="mb-3">
                                                    <label for="pushOrderId" class="form-label">订单号</label>
                                                    <input type="text" class="form-control" id="pushOrderId" 
                                                           placeholder="请输入订单号" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="pushImeiCode" class="form-label">IMEI编码</label>
                                                    <input type="text" class="form-control" id="pushImeiCode"
                                                           placeholder="请输入15位IMEI编码" maxlength="15" required>
                                                    <div class="form-text">IMEI编码必须是15位数字</div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="pushIsSecondHand" class="form-label">是否二手机</label>
                                                    <select class="form-select" id="pushIsSecondHand" required>
                                                        <option value="2" selected>否</option>
                                                        <option value="1">是</option>
                                                    </select>
                                                    <div class="form-text">请选择设备是否为二手机</div>
                                                </div>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="bi bi-upload me-1"></i>推送IMEI
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="text-muted small me-auto">
                                <i class="bi bi-info-circle me-1"></i>
                                权限等级：<span id="userPermissionLevel">-</span>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = new bootstrap.Modal(document.getElementById('notarizationModal'));
    }

    bindEvents() {
        try {
            // 表单提交事件
            const settleOrderForm = document.getElementById('settleOrderForm');
            if (settleOrderForm) {
                settleOrderForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleSettleOrder();
                });
            }

            const queryOrdersForm = document.getElementById('queryOrdersForm');
            if (queryOrdersForm) {
                queryOrdersForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleQueryOrders();
                });
            }

            const unifiedValidationForm = document.getElementById('unifiedValidationForm');
            if (unifiedValidationForm) {
                unifiedValidationForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleUnifiedValidation();
                });
            }

            // 验证范围选择处理
            document.querySelectorAll('input[name="notarizationCheckSources"]').forEach(radio => {
                radio.addEventListener('change', () => {
                    this.updateValidationSourceHelp();
                });
            });

            // 串码输入预览
            const unifiedValidationCodes = document.getElementById('unifiedValidationCodes');
            if (unifiedValidationCodes) {
                unifiedValidationCodes.addEventListener('input', () => {
                    this.updateValidationPreview();
                });
            }

            const pushImeiForm = document.getElementById('pushImeiForm');
            if (pushImeiForm) {
                pushImeiForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handlePushImei();
                });
            }

            // 选项卡切换事件
            document.querySelectorAll('#notarizationTabs button').forEach(tab => {
                tab.addEventListener('shown.bs.tab', (e) => {
                    this.currentTab = e.target.getAttribute('data-bs-target').substring(1);
                    this.clearResults();

                    // 如果切换到统一串码验证标签，加载统计数据
                    if (this.currentTab === 'unified-validation') {
                        this.loadStatistics();
                    }
                });
            });

            // 输入验证
            this.bindInputValidation();
        } catch (error) {
            console.error('绑定事件失败:', error);
            throw error;
        }
    }

    bindInputValidation() {
        try {
            // 身份证号验证
            const queryIdCard = document.getElementById('queryIdCard');
            if (queryIdCard) {
                queryIdCard.addEventListener('input', (e) => {
                    const value = e.target.value.replace(/[^\dXx]/g, '');
                    e.target.value = value;
                });
            }

            // IMEI验证（推送IMEI单个输入）
            const pushImeiCode = document.getElementById('pushImeiCode');
            if (pushImeiCode) {
                pushImeiCode.addEventListener('input', (e) => {
                    const value = e.target.value.replace(/\D/g, '');
                    e.target.value = value;
                });
            }

            // 统一验证串码输入验证和实时预览 - 替换原来的IMEI查询功能
            // 注意：这些事件监听器已经在bindEvents()中的串码输入预览部分处理了
            // 这里不需要重复添加，避免冲突
        } catch (error) {
            console.error('绑定输入验证失败:', error);
            // 不抛出错误，因为这不是关键功能
        }
    }

    async loadUserPermissions() {
        try {
            const response = await fetch('/api/notarization/status');
            const result = await response.json();
            
            if (result.success) {
                const permissions = result.data.user_permissions;
                const level = result.data.current_user_level;
                
                document.getElementById('userPermissionLevel').textContent = 
                    level === 'full' ? '完全权限' : 
                    level === 'standard' ? '标准权限' : 
                    level === 'limited' ? '有限权限' : '未知';
                
                // 根据权限禁用功能
                this.updateUIBasedOnPermissions(permissions);
            }
        } catch (error) {
            console.error('加载用户权限失败:', error);
        }
    }

    updateUIBasedOnPermissions(permissions) {
        // 结清订单和推送IMEI需要标准权限
        if (!permissions.standard) {
            document.getElementById('settle-order-tab').classList.add('disabled');
            document.getElementById('push-imei-tab').classList.add('disabled');
            
            // 添加提示
            const settleCard = document.querySelector('#settle-order .card-body');
            const pushCard = document.querySelector('#push-imei .card-body');
            
            [settleCard, pushCard].forEach(card => {
                if (card) {
                    card.insertAdjacentHTML('afterbegin', 
                        '<div class="alert alert-warning">此功能需要标准权限或以上</div>'
                    );
                }
            });
        }
        
        // 查询功能需要有限权限（所有用户都有）
        if (!permissions.limited) {
            // 禁用所有功能
            document.querySelectorAll('#notarizationTabs button').forEach(tab => {
                tab.classList.add('disabled');
            });
        }
    }

    show() {
        this.modal.show();
    }

    hide() {
        this.modal.hide();
    }

    clearResults() {
        // 安全地隐藏结果显示区域
        const queryOrdersResult = document.getElementById('queryOrdersResult');
        if (queryOrdersResult) {
            queryOrdersResult.style.display = 'none';
        }

        // 隐藏统一验证结果（替换原来的IMEI查询结果）
        const unifiedValidationResult = document.getElementById('unifiedValidationResult');
        if (unifiedValidationResult) {
            unifiedValidationResult.style.display = 'none';
        }
    }

    async handleSettleOrder() {
        const orderId = document.getElementById('settleOrderId').value.trim();

        if (!orderId) {
            this.showAlert('请输入订单号', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `settle-order-${orderId}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        // 二次确认
        if (!confirm(`确认要结清订单 ${orderId} 吗？此操作不可逆！`)) {
            return;
        }

        const finalConfirm = prompt('请输入 "CONFIRM" 确认结清操作：');
        if (finalConfirm !== 'CONFIRM') {
            this.showAlert('确认失败，已取消操作', 'info');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/settle-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(result.message || '订单结清成功', 'success');
                document.getElementById('settleOrderId').value = '';
            } else {
                this.showAlert(result.error || '结清失败', 'danger');
            }
        } catch (error) {
            console.error('结清订单失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
            // 确保全局加载指示器被隐藏
            this.ensureGlobalLoadingHidden();
        }
    }

    async handleQueryOrders() {
        const idCard = document.getElementById('queryIdCard').value.trim();

        if (!idCard) {
            this.showAlert('请输入身份证号', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `query-orders-${idCard}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/query-orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id_card: idCard })
            });

            const result = await response.json();

            if (result.success) {
                this.displayOrderQueryResult(result.data);
            } else {
                this.showAlert(result.error || '查询失败', 'danger');
            }
        } catch (error) {
            console.error('查询订单失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
            // 确保全局加载指示器被隐藏
            this.ensureGlobalLoadingHidden();
        }
    }



    updateValidationSourceHelp() {
        const selectedSource = document.querySelector('input[name="notarizationCheckSources"]:checked').value;
        const helpElement = document.getElementById('notarizationCheckSourcesHelp');

        const helpTexts = {
            'both': '全面验证：同时检查内部数据库和第三方API，确保串码在所有数据源中都可用',
            'internal': '内部数据库：仅检查内部数据库中的串码重复情况',
            'api': '第三方API：仅检查第三方API中的串码重复情况'
        };

        helpElement.textContent = helpTexts[selectedSource];
    }

    updateValidationPreview() {
        const input = document.getElementById('unifiedValidationCodes').value.trim();
        const preview = document.getElementById('validationPreview');
        const previewList = document.getElementById('validationPreviewList');
        const counter = document.getElementById('validationCounter');

        if (input) {
            // 解析串码
            const codes = this.parseSerialNumbers(input);

            if (codes.length > 0) {
                // 更新计数器
                counter.textContent = `${codes.length}个`;
                counter.style.display = 'inline';

                // 更新预览
                previewList.innerHTML = codes.map(code =>
                    `<span class="badge bg-secondary">${code}</span>`
                ).join('');
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
                counter.style.display = 'none';
            }
        } else {
            preview.style.display = 'none';
            counter.style.display = 'none';
        }
    }

    parseSerialNumbers(input) {
        // 解析多种格式的串码输入
        const codes = input
            .split(/[,\s\n\r]+/)
            .map(code => code.trim())
            .filter(code => code.length > 0)
            .filter(code => /^\d{15}$/.test(code)); // 只保留15位数字

        return [...new Set(codes)]; // 去重
    }

    async handleUnifiedValidation() {
        const serialCodes = document.getElementById('unifiedValidationCodes').value.trim();
        const checkSources = document.querySelector('input[name="notarizationCheckSources"]:checked').value;

        if (!serialCodes) {
            this.showAlert('请输入串码', 'warning');
            return;
        }

        // 解析串码
        const parsedCodes = this.parseSerialNumbers(serialCodes);
        if (parsedCodes.length === 0) {
            this.showAlert('请输入有效的15位数字串码', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `unified-validation-${checkSources}-${parsedCodes.join(',')}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/serial-number/unified-check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    serial_numbers: parsedCodes,
                    check_sources: checkSources
                })
            });

            const result = await response.json();
            console.log('统一验证API响应:', result); // 调试日志

            if (result.success) {
                // 检查数据结构
                if (result.data) {
                    this.displayUnifiedValidationResult(result.data, checkSources);
                } else {
                    // 如果没有data字段，直接使用result
                    this.displayUnifiedValidationResult(result, checkSources);
                }
            } else {
                console.error('统一验证失败:', result); // 调试日志
                this.showAlert(result.error || '验证失败', 'danger');
            }
        } catch (error) {
            console.error('统一验证失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
            // 确保全局加载指示器被隐藏
            this.ensureGlobalLoadingHidden();
        }
    }

    async handlePushImei() {
        const orderId = document.getElementById('pushOrderId').value.trim();
        const imeiCode = document.getElementById('pushImeiCode').value.trim();
        const isSecondHand = document.getElementById('pushIsSecondHand').value;

        if (!orderId || !imeiCode) {
            this.showAlert('请填写完整信息', 'warning');
            return;
        }

        // 防止重复请求
        const requestKey = `push-imei-${orderId}-${imeiCode}-${isSecondHand}`;
        if (this.pendingRequests.has(requestKey)) {
            console.log('请求正在处理中，跳过重复请求');
            return;
        }

        this.setLoading(true);
        this.pendingRequests.add(requestKey);

        try {
            const response = await fetch('/api/notarization/push-imei', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    order_id: orderId,
                    imei_code: imeiCode,
                    is_second_hand: isSecondHand
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(result.message || 'IMEI推送成功', 'success');
                document.getElementById('pushOrderId').value = '';
                document.getElementById('pushImeiCode').value = '';
                document.getElementById('pushIsSecondHand').value = '2'; // 重置为默认值
            } else {
                this.showAlert(result.error || '推送失败', 'danger');
            }
        } catch (error) {
            console.error('推送IMEI失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.setLoading(false);
            this.pendingRequests.delete(requestKey);
            // 确保全局加载指示器被隐藏
            this.ensureGlobalLoadingHidden();
        }
    }

    displayOrderQueryResult(data) {
        const resultDiv = document.getElementById('queryOrdersResult');
        const html = `
            <div class="card border-info">
                <div class="card-header bg-info-light">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>查询结果</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-file-text me-2 text-primary"></i>
                                <span>有效公证订单数：<strong>${data.certified_orders}</strong> 个</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-phone me-2 text-success"></i>
                                <span>在租台数：<strong>${data.rented_devices}</strong> 台</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        ${data.can_apply_more ?
                            `<div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>
                                还可以申请 <strong>${data.remaining_slots}</strong> 台设备
                            </div>` :
                            `<div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                已达到最大设备数量限制（5台）
                            </div>`
                        }
                    </div>
                </div>
            </div>
        `;
        resultDiv.innerHTML = html;
        resultDiv.style.display = 'block';
    }

    displayUnifiedValidationResult(data, checkSources) {
        const resultDiv = document.getElementById('unifiedValidationResult');

        // 获取验证源描述
        const sourceDescriptions = {
            'both': '全面验证',
            'internal': '内部数据库',
            'api': '第三方API'
        };

        // 从正确的数据结构中获取摘要信息
        const summary = data.summary || {};
        const total = summary.total || 0;
        const available = summary.available || 0;
        const partialConflict = summary.partial_conflict || 0;
        const blocked = summary.blocked || 0;

        let html = `
            <div class="card border-primary">
                <div class="card-header bg-primary-light">
                    <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>验证结果 - ${sourceDescriptions[checkSources]}</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-primary">${total}</div>
                                <small class="text-muted">总数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success">${available}</div>
                                <small class="text-muted">可用</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-warning">${partialConflict}</div>
                                <small class="text-muted">部分冲突</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-danger">${blocked}</div>
                                <small class="text-muted">已占用</small>
                            </div>
                        </div>
                    </div>
        `;

        // 根据结果显示不同的提示
        if (available === total) {
            html += `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    所有串码都可用，可以继续申请公证
                </div>
            `;
        } else if (blocked > 0 || partialConflict > 0) {
            html += `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    发现冲突的串码，这些串码不可用于申请公证
                </div>
            `;
        }

        // 显示详细结果
        if (data.results && data.results.length > 0) {
            // 按状态分组
            const availableResults = data.results.filter(r => r.status === 'available');
            const partialConflictResults = data.results.filter(r => r.status === 'partial_conflict');
            const blockedResults = data.results.filter(r => r.status === 'blocked');

            // 显示可用的串码
            if (availableResults.length > 0) {
                html += `
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="text-success mb-0"><i class="bi bi-check-circle me-2"></i>可用的串码：</h6>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="notarizationModal.copyAvailableCodes()">
                                <i class="bi bi-clipboard me-1"></i>复制可用串码
                            </button>
                        </div>
                        <div class="d-flex flex-wrap gap-2" id="availableCodesList">
                            ${availableResults.map(result =>
                                `<span class="badge bg-success" title="${result.message}">${result.serial_number}</span>`
                            ).join('')}
                        </div>
                    </div>
                `;
            }

            // 显示部分冲突的串码
            if (partialConflictResults.length > 0) {
                html += `
                    <div class="mt-3">
                        <h6 class="text-warning"><i class="bi bi-exclamation-triangle me-2"></i>部分冲突的串码：</h6>
                        <div class="d-flex flex-wrap gap-2">
                            ${partialConflictResults.map(result =>
                                `<span class="badge bg-warning" title="${result.message}">${result.serial_number}</span>`
                            ).join('')}
                        </div>
                    </div>
                `;
            }

            // 显示已占用的串码
            if (blockedResults.length > 0) {
                html += `
                    <div class="mt-3">
                        <h6 class="text-danger"><i class="bi bi-x-circle me-2"></i>已占用的串码：</h6>
                        <div class="d-flex flex-wrap gap-2">
                            ${blockedResults.map(result =>
                                `<span class="badge bg-danger" title="${result.message}">${result.serial_number}</span>`
                            ).join('')}
                        </div>
                    </div>
                `;
            }
        }

        // 显示自动录入结果
        if (data.auto_insert && data.auto_insert.enabled) {
            const autoInsert = data.auto_insert;
            const successCount = autoInsert.successfully_added ? autoInsert.successfully_added.length : 0;
            const existsCount = autoInsert.already_exists ? autoInsert.already_exists.length : 0;
            const failedCount = autoInsert.failed ? autoInsert.failed.length : 0;

            if (successCount > 0 || existsCount > 0 || failedCount > 0) {
                html += `
                    <div class="mt-3">
                        <div class="card border-info">
                            <div class="card-header bg-info-light">
                                <h6 class="mb-0"><i class="bi bi-database-add me-2"></i>自动录入结果</h6>
                            </div>
                            <div class="card-body">
                `;

                if (successCount > 0) {
                    html += `
                        <div class="alert alert-success mb-2">
                            <i class="bi bi-check-circle me-2"></i>
                            成功录入 <strong>${successCount}</strong> 个新串码到内部数据库
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">新录入的串码：</small>
                            <div class="d-flex flex-wrap gap-1 mt-1">
                                ${autoInsert.successfully_added.map(serial =>
                                    `<span class="badge bg-success-subtle text-success">${serial}</span>`
                                ).join('')}
                            </div>
                        </div>
                    `;
                }

                if (existsCount > 0) {
                    html += `
                        <div class="mb-2">
                            <small class="text-muted">已存在的串码（${existsCount}个）：</small>
                            <div class="d-flex flex-wrap gap-1 mt-1">
                                ${autoInsert.already_exists.map(serial =>
                                    `<span class="badge bg-secondary-subtle text-secondary">${serial}</span>`
                                ).join('')}
                            </div>
                        </div>
                    `;
                }

                if (failedCount > 0) {
                    html += `
                        <div class="alert alert-warning mb-2">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            录入失败 <strong>${failedCount}</strong> 个串码
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">录入失败的串码：</small>
                            <div class="d-flex flex-wrap gap-1 mt-1">
                                ${autoInsert.failed.map(item =>
                                    `<span class="badge bg-warning-subtle text-warning" title="${item.error}">${item.serial_number}</span>`
                                ).join('')}
                            </div>
                        </div>
                    `;
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        html += `
                </div>
            </div>
        `;

        resultDiv.innerHTML = html;
        resultDiv.style.display = 'block';

        // 存储当前验证结果以供复制功能使用
        this.lastValidationResult = data;

        // 如果有自动录入的串码，刷新统计数据
        if (data.auto_insert && data.auto_insert.enabled &&
            data.auto_insert.successfully_added &&
            data.auto_insert.successfully_added.length > 0) {
            // 延迟刷新统计数据，确保数据库已更新
            setTimeout(() => {
                this.refreshStatistics();
            }, 1000);
        }
    }

    copyAvailableCodes() {
        if (!this.lastValidationResult || !this.lastValidationResult.results) {
            this.showAlert('没有可复制的串码', 'warning');
            return;
        }

        const availableResults = this.lastValidationResult.results.filter(r => r.status === 'available');
        if (availableResults.length === 0) {
            this.showAlert('没有可用的串码', 'warning');
            return;
        }

        const codesText = availableResults.map(r => r.serial_number).join('\n');

        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代 Clipboard API
            navigator.clipboard.writeText(codesText).then(() => {
                this.showAlert(`已复制 ${availableResults.length} 个可用串码到剪贴板`, 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyTextToClipboard(codesText);
            });
        } else {
            // 降级方案
            this.fallbackCopyTextToClipboard(codesText);
        }
    }

    fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                this.showAlert(`已复制 ${this.lastQueryResult.available_imeis.length} 个可用IMEI编码到剪贴板`, 'success');
            } else {
                this.showAlert('复制失败，请手动选择复制', 'warning');
            }
        } catch (err) {
            console.error('复制失败:', err);
            this.showAlert('复制失败，请手动选择复制', 'warning');
        } finally {
            document.body.removeChild(textArea);
        }
    }

    showAlert(message, type = 'info') {
        // 创建临时提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到当前活动的选项卡
        const activeTab = document.querySelector('.tab-pane.active .card-body');
        if (activeTab) {
            activeTab.insertBefore(alertDiv, activeTab.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        const buttons = document.querySelectorAll('#notarizationModal button[type="submit"]');

        buttons.forEach(button => {
            if (loading) {
                // 保存原始内容
                if (!button.dataset.originalHtml) {
                    button.dataset.originalHtml = button.innerHTML;
                }
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>处理中...';
            } else {
                button.disabled = false;
                // 恢复原始内容
                if (button.dataset.originalHtml) {
                    button.innerHTML = button.dataset.originalHtml;
                } else {
                    // 备用恢复方案
                    const buttonId = button.closest('form').id;
                    const buttonTexts = {
                        'settleOrderForm': '<i class="bi bi-check-circle me-1"></i>结清订单',
                        'queryOrdersForm': '<i class="bi bi-search me-1"></i>查询订单',
                        'unifiedValidationForm': '<i class="bi bi-search me-1"></i>开始验证',
                        'pushImeiForm': '<i class="bi bi-upload me-1"></i>推送IMEI'
                    };
                    button.innerHTML = buttonTexts[buttonId] || button.innerHTML;
                }
            }
        });
    }

    ensureGlobalLoadingHidden() {
        // 隐藏全局加载指示器
        try {
            // 隐藏base.html中的全局加载遮罩
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay && loadingOverlay.style.display !== 'none') {
                loadingOverlay.style.display = 'none';
                console.log('已隐藏全局加载遮罩');
            }

            // 隐藏performance-enhancer中的加载指示器
            const globalIndicator = document.getElementById('global-loading-indicator');
            if (globalIndicator && globalIndicator.style.display !== 'none') {
                globalIndicator.style.display = 'none';
                console.log('已隐藏全局加载指示器');
            }

            // 调用全局hideLoading函数（如果存在）
            if (typeof window.hideLoading === 'function') {
                window.hideLoading();
            }

            // 调用LoadingController的hide方法（如果存在）
            if (typeof window.LoadingController !== 'undefined' && window.LoadingController.hide) {
                window.LoadingController.hide();
            }
        } catch (error) {
            console.warn('隐藏全局加载指示器时出错:', error);
        }
    }

    async loadStatistics() {
        const statisticsContent = document.getElementById('statisticsContent');

        try {
            // 显示加载状态
            statisticsContent.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm text-info" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <small class="text-muted ms-2">正在加载统计数据...</small>
                </div>
            `;

            const response = await fetch('/api/serial-number/statistics');
            const result = await response.json();

            if (result.success) {
                this.displayStatistics(result.statistics);
            } else {
                this.displayStatisticsError(result.error || '获取统计数据失败');
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            this.displayStatisticsError('网络错误，无法获取统计数据');
        }
    }

    displayStatistics(stats) {
        const statisticsContent = document.getElementById('statisticsContent');

        const html = `
            <div class="row g-3">
                <div class="col-6 col-md-3">
                    <div class="text-center">
                        <div class="h4 text-primary mb-1">
                            <i class="bi bi-database me-1"></i>${stats.total || 0}
                        </div>
                        <small class="text-muted">总串码数</small>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="text-center">
                        <div class="h4 text-success mb-1">
                            <i class="bi bi-plus-circle me-1"></i>${stats.today || 0}
                        </div>
                        <small class="text-muted">今日录入</small>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="text-center">
                        <div class="h4 text-info mb-1">
                            <i class="bi bi-calendar-week me-1"></i>${stats.this_week || 0}
                        </div>
                        <small class="text-muted">本周录入</small>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="text-center">
                        <div class="h4 text-warning mb-1">
                            <i class="bi bi-robot me-1"></i>${stats.auto_inserted || 0}
                        </div>
                        <small class="text-muted">自动录入</small>
                    </div>
                </div>
            </div>
            ${stats.date_info ? `
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        统计时间：${stats.date_info.today} | 本周：${stats.date_info.week_start} 至 ${stats.date_info.week_end}
                    </small>
                </div>
            ` : ''}
        `;

        statisticsContent.innerHTML = html;
    }

    displayStatisticsError(errorMessage) {
        const statisticsContent = document.getElementById('statisticsContent');

        statisticsContent.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <small>${errorMessage}</small>
            </div>
        `;
    }

    refreshStatistics() {
        this.loadStatistics();
    }
}

// 全局实例
window.NotarizationModal = NotarizationModal;

// 自动初始化 - 防止重复初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经初始化
    if (typeof window.notarizationModal === 'undefined') {
        try {
            window.notarizationModal = new NotarizationModal();
            console.log('公证模态框组件初始化成功');
        } catch (error) {
            console.error('公证模态框组件初始化失败:', error);
        }
    } else {
        console.log('公证模态框组件已存在，跳过重复初始化');
    }
});
