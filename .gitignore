# Python相关
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
**/*.pyc
**/*.cpython-*.pyc

# 明确指定项目中的特定 .pyc 文件
app/**/*.cpython-38.pyc
app/**/__pycache__/*.cpython-38.pyc
app/__pycache__/*.cpython-38.pyc
app/models/__pycache__/*.cpython-38.pyc
app/routes/__pycache__/*.cpython-38.pyc
app/services/__pycache__/*.cpython-38.pyc
app/utils/__pycache__/*.cpython-38.pyc

# 虚拟环境
.venv/
venv/
ENV/
env/
.env

# IDE和编辑器
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.cursor/
*.sublime-*
*~
.project
.pydevproject

# 特定项目文件
flask_cache/
.mypy_cache/
.pytest_cache/
.coverage
htmlcov/

# 缓存文件
cache/
cache/*
tmp/
.tmp/

# 日志文件
*.log
logs/
log/

# 本地配置文件
.env
.env.local
.env.*.local
instance/
local_settings.py

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 例外：允许特定数据库文件
!serial_numbers.db

# 临时生成的文件
*.pid
*.bak
*.swp
*.tmp

# 可执行文件和压缩包
*.exe
*.zip
*.tar.gz
*.rar
*.7z

# 媒体和上传文件（视情况而定是否需要排除）
# media/
# uploads/

# 敏感信息文件
secrets.yaml
credentials.json
*.pem
*.key

# 生成的文档文件
/generated_docs/
/site/

# 特定于该项目的临时文件
# 根据项目需要可能需要排除特定的DOCX文件或其他临时生成文件
# AHT/*.tmp
# BHT/*.tmp
# HZ/*.tmp

# 新增排除规则
.trunk
/trunk

# Node.js dependencies
node_modules/
# 数据库文件
serial_numbers.db
*.db.backup*
