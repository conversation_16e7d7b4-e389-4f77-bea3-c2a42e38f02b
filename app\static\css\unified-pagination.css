/**
 * 统一分页样式文件
 * 解决DataTables分页功能在不同屏幕尺寸下的自适应问题
 * 优先级：高于其他分页样式，确保统一性
 */

/* ==========================================================================
   基础分页容器样式
   ========================================================================== */

.dataTables_wrapper .dataTables_paginate {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.75rem 0 0.5rem 0; /* 减少默认边距 */
    padding: 0.375rem; /* 减少默认内边距 */
    font-size: 0.875rem;
    border-radius: 0.3rem; /* 稍微减小圆角 */
    background-color: rgba(248, 249, 250, 0.4); /* 降低背景透明度 */
    transition: all 0.25s ease;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    /* 隐藏滚动条但保持可滚动 */
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.dataTables_wrapper .dataTables_paginate::-webkit-scrollbar {
    display: none;
}

/* ==========================================================================
   分页按钮基础样式
   ========================================================================== */

.dataTables_wrapper .dataTables_paginate .paginate_button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    min-height: 40px;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    color: #495057;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    user-select: none;
    box-sizing: border-box;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: #0056b3;
    background-color: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.15);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.25);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.6;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* ==========================================================================
   前后页按钮特殊样式
   ========================================================================== */

.dataTables_wrapper .dataTables_paginate .paginate_button.previous,
.dataTables_wrapper .dataTables_paginate .paginate_button.next {
    font-weight: 600;
    min-width: 50px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.previous::before {
    content: "‹";
    margin-right: 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.next::after {
    content: "›";
    margin-left: 0.25rem;
}

/* ==========================================================================
   分页信息样式
   ========================================================================== */

.dataTables_wrapper .dataTables_info {
    padding: 0.5rem 0; /* 减少默认内边距 */
    margin: 0;
    font-size: 0.8125rem; /* 稍微减小字体 */
    color: #6c757d;
    text-align: center;
    line-height: 1.3; /* 减少行高 */
}

/* ==========================================================================
   平板端优化 (768px - 1024px)
   ========================================================================== */

@media (min-width: 769px) and (max-width: 1024px) {
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        min-width: 36px;
        min-height: 36px;
        padding: 0.375rem 0.625rem;
        margin: 0 0.1rem;
        font-size: 0.8125rem;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
    .dataTables_wrapper .dataTables_paginate .paginate_button.next {
        min-width: 44px;
    }
    
    .dataTables_wrapper .dataTables_info {
        font-size: 0.8125rem;
        padding: 0.5rem 0;
    }
}

/* ==========================================================================
   移动端优化 (≤768px)
   ========================================================================== */

@media (max-width: 768px) {
    .dataTables_wrapper .dataTables_paginate {
        justify-content: center;
        padding: 0.375rem;
        margin: 0.75rem 0;
        gap: 0.25rem;
        flex-wrap: nowrap;
        overflow-x: auto;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        min-width: 44px;
        min-height: 44px;
        padding: 0.5rem;
        margin: 0;
        font-size: 0.875rem;
        border-radius: 0.5rem;
        flex-shrink: 0;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
    .dataTables_wrapper .dataTables_paginate .paginate_button.next {
        min-width: 50px;
        font-weight: 700;
    }
    
    /* 移动端智能隐藏页码按钮 */
    .dataTables_wrapper .dataTables_paginate.mobile-smart-hide .paginate_button:not(.previous):not(.next):not(.current) {
        display: none;
    }
    
    .dataTables_wrapper .dataTables_info {
        font-size: 0.8125rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        margin-top: 0.5rem;
    }
}

/* ==========================================================================
   超小屏幕优化 (≤480px)
   ========================================================================== */

@media (max-width: 480px) {
    .dataTables_wrapper .dataTables_paginate {
        padding: 0.25rem;
        margin: 0.5rem 0;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        min-width: 40px;
        min-height: 40px;
        padding: 0.375rem;
        font-size: 0.8125rem;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
    .dataTables_wrapper .dataTables_paginate .paginate_button.next {
        min-width: 46px;
    }
    
    /* 超小屏幕只显示前后页和当前页 */
    .dataTables_wrapper .dataTables_paginate.ultra-compact .paginate_button:not(.previous):not(.next):not(.current) {
        display: none !important;
    }
    
    .dataTables_wrapper .dataTables_info {
        font-size: 0.75rem;
        padding: 0.375rem;
    }
}

/* ==========================================================================
   紧凑模式 - 极致空间利用
   ========================================================================== */

/* 紧凑模式：进一步减少所有间距 */
.dataTables_wrapper.compact-mode .dataTables_paginate {
    margin: 0.25rem 0 0.125rem 0;
    padding: 0.2rem;
    background-color: transparent; /* 移除背景 */
}

.dataTables_wrapper.compact-mode .dataTables_paginate .paginate_button {
    min-width: 28px;
    min-height: 28px;
    padding: 0.2rem 0.3rem;
    margin: 0 0.05rem;
    font-size: 0.7rem;
    border-radius: 0.2rem;
}

.dataTables_wrapper.compact-mode .dataTables_info {
    margin: 0.125rem 0 0.25rem 0;
    padding: 0.15rem 0.25rem;
    font-size: 0.65rem;
    line-height: 1;
    background-color: transparent;
    border: none;
}

/* 窄容器中的紧凑模式 */
.col-md-6 .dataTables_wrapper.compact-mode .dataTables_paginate {
    margin: 0.2rem 0 0.1rem 0;
    padding: 0.15rem;
}

.col-md-6 .dataTables_wrapper.compact-mode .dataTables_info {
    margin: 0.1rem 0 0.2rem 0;
    padding: 0.1rem 0.2rem;
    font-size: 0.6rem;
}

/* ==========================================================================
   特殊状态和辅助类
   ========================================================================== */

/* 当前页指示器（用于超小屏幕） */
.current-page-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.2);
    border-radius: 0.25rem;
    margin: 0 0.25rem;
}

/* 省略号指示器 */
.ellipsis-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 30px;
    padding: 0.375rem;
    color: #6c757d;
    font-weight: 600;
}

/* 滚动提示 */
.pagination-scroll-hint {
    text-align: center;
    color: #6c757d;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

/* 隐藏类 */
.mobile-hidden {
    display: none !important;
}

/* ==========================================================================
   汇总页面专用样式 - 优化窄容器中的分页布局
   ========================================================================== */

/* 汇总页面的分页容器特殊处理 - 紧凑布局 */
.col-md-6 .dataTables_wrapper .dataTables_paginate {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    margin: 0.5rem 0 0.25rem 0; /* 减少上下边距 */
    padding: 0.25rem 0; /* 减少内边距 */
}

.col-md-6 .dataTables_wrapper .dataTables_paginate .paginate_button {
    flex-shrink: 0;
    min-width: 36px; /* 稍微减小按钮最小宽度 */
    min-height: 36px;
    padding: 0.375rem 0.5rem; /* 减少按钮内边距 */
    margin: 0 0.1rem; /* 减少按钮间距 */
    font-size: 0.8125rem; /* 稍微减小字体 */
}

/* 汇总页面的信息文本样式 - 紧凑设计 */
.col-md-6 .dataTables_wrapper .dataTables_info {
    width: 100%;
    text-align: center;
    margin: 0.25rem 0 0.5rem 0; /* 大幅减少上下边距 */
    padding: 0.25rem 0.5rem; /* 减少内边距 */
    background-color: rgba(248, 249, 250, 0.6); /* 降低背景透明度 */
    border-radius: 0.2rem; /* 减小圆角 */
    font-size: 0.75rem; /* 减小字体 */
    color: #6c757d;
    border: 1px solid rgba(222, 226, 230, 0.3); /* 降低边框透明度 */
    line-height: 1.2; /* 减少行高 */
}

/* 确保分页容器在窄容器中有足够的空间 */
.col-md-6 .dataTables_wrapper .row {
    margin-left: 0;
    margin-right: 0;
}

.col-md-6 .dataTables_wrapper .row > [class*="col-"] {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}

/* 移动端紧凑布局优化 */
@media (max-width: 768px) {
    .col-md-6 .dataTables_wrapper .dataTables_paginate {
        justify-content: center; /* 居中显示 */
        padding: 0.25rem;
        margin: 0.375rem 0 0.125rem 0; /* 进一步减少边距 */
    }

    .col-md-6 .dataTables_wrapper .dataTables_paginate .paginate_button {
        min-width: 32px; /* 移动端更小的按钮 */
        min-height: 32px;
        padding: 0.25rem 0.375rem;
        margin: 0 0.05rem; /* 更小的间距 */
        font-size: 0.75rem;
    }

    .col-md-6 .dataTables_wrapper .dataTables_info {
        margin: 0.125rem 0 0.375rem 0; /* 大幅减少边距 */
        font-size: 0.7rem;
        padding: 0.2rem 0.375rem;
        line-height: 1.1;
    }
}

/* 超小屏幕下极致紧凑 */
@media (max-width: 480px) {
    .col-md-6 .dataTables_wrapper .dataTables_paginate {
        padding: 0.2rem;
        margin: 0.25rem 0 0.1rem 0; /* 极小边距 */
    }

    .col-md-6 .dataTables_wrapper .dataTables_paginate .paginate_button {
        min-width: 30px;
        min-height: 30px;
        padding: 0.2rem 0.3rem;
        font-size: 0.7rem;
    }

    .col-md-6 .dataTables_wrapper .dataTables_info {
        margin: 0.1rem 0 0.25rem 0;
        font-size: 0.65rem;
        padding: 0.15rem 0.25rem;
        line-height: 1;
    }
}

/* ==========================================================================
   动画和过渡效果
   ========================================================================== */

.dataTables_wrapper .dataTables_paginate .paginate_button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:active {
    transform: translateY(0);
    transition-duration: 0.1s;
}

/* 加载状态 */
.dataTables_wrapper.loading .dataTables_paginate {
    opacity: 0.6;
    pointer-events: none;
}

/* ==========================================================================
   高对比度和无障碍支持
   ========================================================================== */

@media (prefers-contrast: high) {
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        border-width: 2px;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        border-color: #000;
        background-color: #000;
    }
}

/* 焦点状态 */
.dataTables_wrapper .dataTables_paginate .paginate_button:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* ==========================================================================
   打印样式
   ========================================================================== */

@media print {
    .dataTables_wrapper .dataTables_paginate,
    .dataTables_wrapper .dataTables_info {
        display: none !important;
    }
}
