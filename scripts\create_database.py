#!/usr/bin/env python3
"""
创建PostgreSQL数据库脚本
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys

def create_database():
    """
    创建新的PostgreSQL数据库
    """
    # 连接参数
    host = "*************"
    port = 5433
    user = "flask_user"
    password = "flask_password"
    new_db_name = "hdsc_serial_numbers_db"
    
    try:
        # 连接到默认的postgres数据库
        print(f"正在连接到PostgreSQL服务器 {host}:{port}...")
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"  # 连接到默认数据库
        )
        
        # 设置自动提交模式
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查数据库是否已存在
        cursor.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s",
            (new_db_name,)
        )
        
        if cursor.fetchone():
            print(f"✅ 数据库 '{new_db_name}' 已存在")
        else:
            # 创建新数据库
            print(f"正在创建数据库 '{new_db_name}'...")
            cursor.execute(f'CREATE DATABASE "{new_db_name}"')
            print(f"✅ 数据库 '{new_db_name}' 创建成功")
        
        cursor.close()
        conn.close()
        
        # 测试新数据库连接
        print(f"正在测试新数据库连接...")
        test_conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=new_db_name
        )
        test_conn.close()
        print(f"✅ 新数据库连接测试成功")
        
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    print("=" * 50)
    print("PostgreSQL数据库创建工具")
    print("=" * 50)
    
    success = create_database()
    
    if success:
        print("\n✅ 数据库创建完成！")
        print("现在可以运行迁移脚本了。")
        sys.exit(0)
    else:
        print("\n❌ 数据库创建失败！")
        sys.exit(1)

if __name__ == '__main__':
    main()