from flask import Blueprint, jsonify, request, session, send_file
from flask_login import login_required, current_user
import logging
from datetime import datetime
import pandas as pd
from io import BytesIO
import tempfile
import os
import time

from app.services.data_service import (
    get_filtered_data, 
    get_overdue_orders, 
    get_orders_by_customer,
    get_customer_summary,
    process_filter_data,
    process_overdue_data,
    process_new_customer_summary_format,
    process_period_data
)
from app.utils.api_client import make_api_request, ApiClient
from app.config import Config
from app.services.home_service import get_home_stats_cached

# 配置日志
logger = logging.getLogger(__name__)

# 创建API蓝图
api = Blueprint('api', __name__)

# 添加API客户端实例
api_client = ApiClient()

@api.route('/home_stats', methods=['GET'])
@login_required
def home_stats():
    """返回首页统计（只读缓存），用于前端无刷新更新。"""
    try:
        stats = get_home_stats_cached()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取首页统计失败: {e}")
        return jsonify({'today_orders': 0, 'overdue_orders': 0, 'system_status': 'unknown', 'api_status': 'unknown'}), 200

# 生成缓存键
def generate_cache_key(endpoint, params=None):
    """生成缓存键"""
    if params is None:
        params = {}
    
    # 将参数排序以确保相同参数生成相同的键
    sorted_params = sorted(params.items())
    params_str = '_'.join([f"{k}={v}" for k, v in sorted_params])
    
    # 生成键
    return f"{endpoint}_{params_str}"

# 保存到session缓存
def save_to_session_cache(endpoint, params, data):
    """
    将数据保存到会话缓存
    
    Args:
        endpoint: API端点名称
        params: 请求参数字典
        data: 要缓存的数据
    """
    cache_key = generate_cache_key(endpoint, params)
    
    # 缓存数据，包含时间戳
    cache_data = {
        'timestamp': datetime.now().timestamp(),
        'data': data
    }
    
    # 创建session缓存区域，如果不存在
    if 'api_cache' not in session:
        session['api_cache'] = {}
    
    # 存储数据
    session['api_cache'][cache_key] = cache_data
    session.modified = True
    
    logger.info(f"数据已保存到会话缓存: {endpoint}")

# 从session缓存获取
def get_from_session_cache(endpoint, params, max_age=1800):
    """
    从会话缓存获取数据
    
    Args:
        endpoint: API端点名称
        params: 请求参数字典
        max_age: 最大缓存时间（秒），默认30分钟
        
    Returns:
        缓存的数据，如果未找到或已过期则返回None
    """
    # 检查是否有API缓存区域
    if 'api_cache' not in session:
        return None
    
    cache_key = generate_cache_key(endpoint, params)
    
    # 检查键是否存在
    if cache_key not in session['api_cache']:
        return None
    
    cache_data = session['api_cache'][cache_key]
    
    # 检查缓存是否过期
    now = datetime.now().timestamp()
    if now - cache_data['timestamp'] > max_age:
        # 删除过期数据
        del session['api_cache'][cache_key]
        session.modified = True
        logger.info(f"会话缓存数据已过期: {endpoint}")
        return None
    
    logger.info(f"从会话缓存获取数据: {endpoint}")
    return cache_data['data']


@api.route('/filter_data', methods=['GET'])
@login_required
def filter_data_api():
    """根据日期筛选数据的API"""
    date = request.args.get('date')
    if not date:
        return jsonify({'error': '未提供日期参数'}), 400
    
    # 尝试从会话缓存获取数据
    params = {'date': date}
    cached_data = get_from_session_cache('filter_data', params)
    if cached_data:
        return jsonify(cached_data)
    
    # 从服务获取数据
    results = get_filtered_data(date)
    
    # 处理期数列数据，将账单状态与期数关联
    results = process_period_data(results)
    
    # 存入会话缓存
    save_to_session_cache('filter_data', params, results)
    
    return jsonify(results)


@api.route('/filter_overdue_orders', methods=['GET'])
@login_required
def overdue_orders_api():
    """获取逾期订单数据的API"""
    if not current_user.has_permission('standard'):
        return jsonify({'error': '权限不足'}), 403
    
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 100, type=int)
    
    # 尝试从会话缓存获取数据
    cache_key = f'filter_overdue_orders_page_{page}_limit_{limit}'
    cached_data = get_from_session_cache(cache_key, {})
    if cached_data:
        return jsonify(cached_data)
    
    # 从服务获取数据
    results = get_overdue_orders(page=page, limit=limit)
    
    # 存入会话缓存
    save_to_session_cache(cache_key, {}, results)
    
    return jsonify(results)


@api.route('/filter_orders_by_customer_name', methods=['GET'])
@login_required
def customer_orders_api():
    """获取客户订单数据的API"""
    customer_name = request.args.get('customer_name')
    if not customer_name:
        return jsonify({'error': '未提供客户姓名参数'}), 400
    
    # 尝试从会话缓存获取数据
    params = {'customer_name': customer_name}
    cached_data = get_from_session_cache(
        'filter_orders_by_customer_name', params
    )
    if cached_data:
        return jsonify(cached_data)
    
    # 从服务获取数据
    results = get_orders_by_customer(customer_name)
    
    # 处理期数列数据，将账单状态与期数关联
    results = process_period_data(results)
    
    # 存入会话缓存
    save_to_session_cache('filter_orders_by_customer_name', params, results)
    
    return jsonify(results)


@api.route('/customer_summary', methods=['GET'])
@login_required
def customer_summary_api():
    """获取客户订单汇总数据的API"""
    if not current_user.has_permission('standard'):
        return jsonify({'error': '权限不足'}), 403
    
    customer_name = request.args.get('customer_name')
    if not customer_name:
        return jsonify({'error': '未提供客户姓名参数'}), 400
    
    # 尝试从会话缓存获取数据
    params = {'customer_name': customer_name}
    # 客户汇总缓存10分钟
    cached_data = get_from_session_cache('customer_summary', params, 600)
    if cached_data:
        return jsonify(cached_data)
    
    # 从服务获取数据
    summary_data = get_customer_summary(customer_name)
    
    # 存入会话缓存
    save_to_session_cache('customer_summary', params, summary_data)
    
    return jsonify(summary_data)


@api.route('/summary_data', methods=['GET'])
@login_required
def summary_data_api():
    """获取汇总数据的API"""
    if not current_user.has_permission('full'):
        return jsonify({'error': '权限不足'}), 403
    
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date or not end_date:
        return jsonify({'error': '未提供日期范围参数'}), 400
    
    # 尝试从会话缓存获取数据
    params = {'start_date': start_date, 'end_date': end_date}
    # 汇总数据缓存30分钟
    cached_data = get_from_session_cache('summary_data', params, 1800)
    if cached_data:
        return jsonify(cached_data)
    
    # 使用数据服务层获取汇总数据
    from app.services.data_service import get_order_summary
    summary_data = get_order_summary(start_date, end_date)
    
    # 存入会话缓存
    save_to_session_cache('summary_data', params, summary_data)
    
    return jsonify(summary_data)


@api.route('/order_summary', methods=['GET'])
@login_required
def order_summary_api():
    """获取订单汇总数据的API"""
    if not current_user.has_permission('full'):
        return jsonify({'error': '权限不足'}), 403
    
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date or not end_date:
        return jsonify({'error': '未提供日期范围参数'}), 400
    
    # 尝试从会话缓存获取数据
    params = {'start_date': start_date, 'end_date': end_date}
    # 订单汇总缓存30分钟
    cached_data = get_from_session_cache('order_summary', params, 1800)
    if cached_data:
        return jsonify(cached_data)
    
    # 使用数据服务层获取逾期汇总数据
    from app.services.data_service import get_overdue_summary
    summary_data = get_overdue_summary(start_date, end_date)
    
    # 存入会话缓存
    save_to_session_cache('order_summary', params, summary_data)
    
    return jsonify(summary_data)


def adapt_api_params(endpoint, params):
    """
    适配API参数，确保与新的API接口兼容
    
    Args:
        endpoint: API端点名称
        params: 原始参数字典
        
    Returns:
        适配后的参数字典
    """
    # 拷贝原始参数，避免修改原始字典
    adapted_params = params.copy()
    
    # 确保API密钥一定存在
    if 'api_key' not in adapted_params:
        adapted_params['api_key'] = Config.API_KEY
    
    # 客户名参数适配
    if 'customer_name' in adapted_params and endpoint == 'customer_summary_db':
        # 部分接口可能使用不同的客户名参数名称
        if 'customer' not in adapted_params:
            adapted_params['customer'] = adapted_params['customer_name']
    
    # 日期格式检查（确保符合YYYY-MM-DD格式）
    for date_field in ['date', 'start_date', 'end_date']:
        if date_field in adapted_params:
            try:
                # 简单验证日期格式
                date_value = adapted_params[date_field]
                if not isinstance(date_value, str) or len(date_value.split('-')) != 3:
                    logger.warning(f"日期字段 {date_field} 格式可能不正确: {date_value}")
            except Exception as e:
                logger.error(f"检查日期字段 {date_field} 时出错: {str(e)}")
    
    return adapted_params


@api.route('/proxy/<path:endpoint>', methods=['GET', 'POST'])
@login_required
def api_proxy(endpoint):
    """API代理，用于转发请求到原始API服务器"""
    # 检查端点是否需要添加_db后缀
    if not endpoint.endswith('_db') and endpoint not in ['ping', 'version']:
        endpoint = f"{endpoint}_db"
        
    if request.method == 'GET':
        # 获取并适配参数
        params = adapt_api_params(endpoint, request.args.to_dict())
        response = make_api_request('GET', endpoint, params)
    else:
        data = request.get_json() or {}
        # 适配参数
        params = adapt_api_params(endpoint, {'api_key': Config.API_KEY})
        response = make_api_request('POST', endpoint, params, data)
    
    return jsonify(response)


@api.route('/ping', methods=['GET'])
def ping():
    """简单的ping端点，用于检测API状态"""
    return jsonify({
        'status': 'ok',
        'message': 'API服务正常运行',
        'timestamp': datetime.now().timestamp()
    })


@api.route('/filter', methods=['GET'])
def get_filter_data():
    """获取筛选数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 记录请求信息
        logger.info(f"获取筛选数据 - 参数: start_date={start_date}, end_date={end_date}")
        
        # 通过API客户端获取数据
        data = api_client.get_filter_data(start_date, end_date)
        
        # 处理数据
        processed_data = process_filter_data(data)
        
        return jsonify(processed_data)
    except Exception as e:
        logger.error(f"获取筛选数据出错: {str(e)}")
        return jsonify({"error": str(e), "results": []}), 500


@api.route('/customer', methods=['GET'])
def get_customer_data():
    """获取客户数据"""
    try:
        # 获取查询参数
        customer_name = request.args.get('name')
        
        # 记录请求信息
        logger.info(f"获取客户数据 - 参数: name={customer_name}")
        
        # 通过API客户端获取数据
        data = api_client.get_customer_data(customer_name)
        
        # 直接返回客户数据，由前端判断是否需要进一步处理
        return jsonify(data)
    except Exception as e:
        logger.error(f"获取客户数据出错: {str(e)}")
        return jsonify({"error": str(e), "results": []}), 500


@api.route('/process_customer_data', methods=['POST'])
def process_customer_data():
    """处理新的客户汇总JSON格式数据"""
    try:
        # 获取POST请求的JSON数据
        data = request.json
        
        if not data:
            logger.error("处理客户数据失败: 未收到数据")
            return jsonify({"error": "未收到数据", "results": []}), 400
        
        # 记录请求信息
        logger.info(f"处理客户汇总数据 - 数据类型: {type(data)}")
        
        # 使用专门的函数处理新的JSON格式
        processed_data = process_new_customer_summary_format(data)
        
        return jsonify(processed_data)
    except Exception as e:
        logger.error(f"处理客户汇总数据出错: {str(e)}")
        return jsonify({"error": str(e), "results": []}), 500


@api.route('/overdue', methods=['GET'])
def get_overdue_data():
    """获取逾期数据"""
    try:
        # 记录请求信息
        logger.info("获取逾期数据")
        
        # 通过API客户端获取数据
        data = api_client.get_overdue_data()
        
        # 处理数据
        processed_data = process_overdue_data(data)
        
        return jsonify(processed_data)
    except Exception as e:
        logger.error(f"获取逾期数据出错: {str(e)}")
        return jsonify({"error": str(e), "results": []}), 500


@api.route('/status', methods=['GET'])
def get_api_status():
    """获取API状态"""
    try:
        # 记录请求信息
        logger.info("检查API状态")
        
        # 通过API客户端检查状态
        status = api_client.check_status()
        
        return jsonify(status)
    except Exception as e:
        logger.error(f"检查API状态出错: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500

# 导出所有逾期订单数据
@api.route('/export_all_overdue', methods=['GET'])
@login_required
def export_all_overdue():
    """导出所有逾期订单数据（不分页），支持搜索过滤"""
    if not current_user.has_permission('standard'):
        return jsonify({'error': '权限不足'}), 403
        
    # 获取导出格式和搜索查询
    export_format = request.args.get('format', 'excel')
    search_query = request.args.get('search', '')
    
    if export_format not in ['excel', 'csv']:
        return jsonify({'error': '不支持的导出格式'}), 400
        
    logger.info(f"导出请求：格式={export_format}, 搜索查询={search_query}")
    
    try:
        # 直接从缓存获取完整数据，而不是分页数据
        logger.info("从缓存获取完整逾期订单数据用于导出")
        
        # 获取缓存键名
        from app import cache
        cache_key = "overdue_data_full"
        
        # 尝试从缓存获取完整数据
        full_data = cache.get(cache_key)
        
        if not full_data:
            # 如果缓存中没有数据，则强制刷新一次
            logger.info("缓存中没有数据，强制获取全部数据")
            all_data = get_overdue_orders(page=1, limit=100000, force_refresh=True, search_query=search_query)
        else:
            # 如果缓存中有完整数据
            logger.info("从缓存获取到完整数据")
            
            if search_query:
                # 如果有搜索查询，则使用filter_data处理搜索过滤（不分页）
                logger.info(f"应用搜索过滤：{search_query}")
                from app.services.data_service import filter_data
                # 使用filter_data函数，专为导出功能设计，不包含分页逻辑
                all_data = filter_data(full_data, search_query=search_query)
            else:
                # 如果没有搜索查询，直接使用完整数据
                logger.info("导出全部数据，不需要过滤")
                all_data = full_data
        
        if 'error' in all_data:
            return jsonify({'error': all_data['error']}), 500
            
        # 提取数据和列名
        results = all_data.get('results', [])
        columns = all_data.get('columns', [])
        
        if not results or not columns:
            return jsonify({'error': '没有可导出的数据'}), 404
            
        logger.info(f"准备导出{len(results)}条逾期订单记录{' (已筛选)' if search_query else ''}")
        
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 记录导出信息
        export_info = f"导出{len(results)}条数据"
        if search_query:
            export_info += f"，搜索条件：{search_query}"
        logger.info(export_info)
        
        # 确保列顺序与页面显示一致
        # 如果列存在，则按照columns中的顺序重新排列
        if not df.empty and all(col in df.columns for col in columns):
            df = df[columns]
        
        # 创建输出缓冲区
        output = BytesIO()
        
        if export_format == 'excel':
            # Excel格式导出 - 使用openpyxl引擎
            try:
                # 尝试使用openpyxl引擎
                df.to_excel(output, sheet_name='逾期订单', index=False, engine='openpyxl')
            except ImportError:
                # 如果openpyxl也不可用，则使用默认引擎
                logger.warning("无法使用openpyxl引擎，尝试使用默认引擎")
                df.to_excel(output, sheet_name='逾期订单', index=False)
            
            # 设置响应头
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            filename = f"逾期订单_全部数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        else:
            # CSV格式导出
            df.to_csv(output, index=False, encoding='utf-8-sig')
            mimetype = 'text/csv'
            filename = f"逾期订单_全部数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 重置文件指针到开始位置
        output.seek(0)
        
        logger.info(f"逾期订单数据导出成功，格式: {export_format}, 文件名: {filename}")
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
        
    except Exception as e:
        logger.exception(f"导出逾期订单数据时发生错误: {str(e)}")
        return jsonify({'error': f'导出过程发生错误: {str(e)}'}), 500

@api.route('/clear_overdue_cache', methods=['GET'])
@login_required
def clear_overdue_cache():
    """清除逾期订单相关的缓存"""
    if not current_user.has_permission('admin'):
        return jsonify({'error': '权限不足'}), 403
    
    try:
        # 从数据服务层导入缓存对象
        from app.services.data_service import cache
        
        # 清除get_overdue_orders函数的缓存
        cache.delete_memoized(get_overdue_orders)
        
        # 清除会话缓存
        session.pop('filter_overdue_orders', None)
        
        # 清除预加载的每日缓存数据
        cache.delete('daily_overdue_orders_count')
        cache.delete('daily_overdue_orders_data')
        
        logger.info("逾期订单缓存已清除")
        return jsonify({'success': True, 'message': '逾期订单缓存已清除'})
    except ImportError as e:
        logger.error(f"导入缓存对象出错: {str(e)}")
        return jsonify({'error': f'导入缓存对象失败: {str(e)}'}), 500
    except AttributeError as e:
        logger.error(f"缓存对象属性错误: {str(e)}")
        return jsonify({'error': f'缓存对象属性错误: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"清除缓存时发生错误: {str(e)}")
        return jsonify({'error': f'清除缓存失败: {str(e)}'}), 500


@api.route('/enterprise-credit/stream', methods=['POST', 'OPTIONS'])
@login_required
def enterprise_credit_stream():
    """企业信用查询流式API代理"""
    # 处理CORS预检请求
    if request.method == 'OPTIONS':
        from flask import Response
        response = Response()
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'POST'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response

    try:
        import requests
        import json
        from flask import Response

        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({'error': '缺少input参数'}), 400

        # 获取查询参数
        query_input = data['input']
        streaming_enabled = data.get('streaming', True)
        display_speed = data.get('speed', 0.001)

        # Coze API配置（从配置读取，避免硬编码）
        API_TOKEN = getattr(Config, 'COZE_API_TOKEN', '')
        WORKFLOW_ID = getattr(Config, 'COZE_WORKFLOW_ID', '')
        if not API_TOKEN or not WORKFLOW_ID:
            logger.error('Coze配置缺失，请设置 COZE_API_TOKEN 和 COZE_WORKFLOW_ID')
            return jsonify({'error': '服务未配置', 'message': '缺少Coze配置'}), 503
        BASE_URL = 'https://api.coze.cn'
        STREAM_URL = f"{BASE_URL}/v1/workflow/stream_run"

        payload = {
            "workflow_id": WORKFLOW_ID,
            "parameters": {
                "input": query_input,
                "streaming": streaming_enabled,
                "speed": display_speed
            }
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN}",
            "Content-Type": "application/json; charset=utf-8"
        }

        logger.info(f"企业信用查询请求: {query_input}, 流式传输: {streaming_enabled}, 显示速度: {display_speed}")

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试第 {attempt + 1} 次请求 Coze API...")
                response = requests.post(
                    STREAM_URL,
                    headers=headers,
                    json=payload,
                    stream=True,
                    timeout=600  # 设置超时时间为10分钟，适应Coze API长时间处理
                )
                logger.info(f"Coze API响应状态码: {response.status_code}")
                break  # 成功则跳出重试循环
            except requests.exceptions.Timeout:
                logger.warning(f"第 {attempt + 1} 次请求超时")
                if attempt == max_retries - 1:  # 最后一次尝试
                    logger.error("所有重试均超时，返回错误")
                    return jsonify({
                        'error': 'API请求超时，请稍后重试',
                        'message': 'Coze API响应时间过长，请检查网络连接或稍后重试'
                    }), 504
                time.sleep(2)  # 重试前等待2秒
            except requests.exceptions.ConnectionError as e:
                logger.error(f"第 {attempt + 1} 次连接错误: {str(e)}")
                if attempt == max_retries - 1:
                    return jsonify({
                        'error': '网络连接错误',
                        'message': f'无法连接到Coze API: {str(e)}'
                    }), 502
                time.sleep(2)
            except Exception as e:
                logger.error(f"第 {attempt + 1} 次请求出现未知错误: {str(e)}")
                if attempt == max_retries - 1:
                    return jsonify({
                        'error': '请求失败',
                        'message': f'企业信用查询服务暂时不可用: {str(e)}'
                    }), 500
                time.sleep(2)

        if response.status_code != 200:
            logger.error(f"Coze API请求失败: {response.status_code}, {response.text}")
            return jsonify({
                'error': f'API请求失败: {response.status_code}',
                'message': response.text
            }), response.status_code

        def generate():
            try:
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=False):
                    if chunk:
                        try:
                            chunk_text = chunk.decode('utf-8')
                        except UnicodeDecodeError:
                            chunk_text = chunk.decode('utf-8', errors='ignore')
                        yield chunk_text
            except Exception as e:
                logger.error(f"流式响应处理错误: {str(e)}")
                yield f"data: {json.dumps({'error': str(e)})}\n\n"

        flask_response = Response(
            generate(),
            mimetype='text/plain'
        )
        flask_response.headers['Cache-Control'] = 'no-cache'
        flask_response.headers['Connection'] = 'keep-alive'
        flask_response.headers['Access-Control-Allow-Origin'] = '*'
        return flask_response

    except Exception as e:
        logger.error(f"企业信用查询服务器错误: {str(e)}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500


# 串码查重相关API
@api.route('/serial-number/check', methods=['POST'])
@login_required
def check_serial_number():
    """检查单个串码是否存在"""
    try:
        data = request.get_json()
        if not data or 'serial_number' not in data:
            return jsonify({'success': False, 'error': '缺少串码参数'}), 400

        serial_number = data['serial_number']

        # 导入串码服务
        from app.services.serial_number_service_v2 import get_serial_service
        service = get_serial_service()

        # 检查串码
        result = service.check_serial_number(serial_number)

        return jsonify(result)

    except Exception as e:
        logger.error(f"检查串码时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': f'服务器错误: {str(e)}'}), 500


@api.route('/serial-number/add', methods=['POST'])
@login_required
def add_serial_number():
    """添加单个串码"""
    try:
        data = request.get_json()
        if not data or 'serial_number' not in data:
            return jsonify({'success': False, 'error': '缺少串码参数'}), 400

        serial_number = data['serial_number']
        notes = data.get('notes', '')
        created_by = current_user.username if current_user else '未知用户'

        # 导入串码服务V2（支持PostgreSQL）
        from app.services.serial_number_service_v2 import get_serial_service
        service = get_serial_service()

        # 添加串码
        result = service.add_serial_number(serial_number, created_by, notes)

        return jsonify(result)

    except Exception as e:
        logger.error(f"添加串码时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': f'服务器错误: {str(e)}'}), 500


@api.route('/serial-number/batch-process', methods=['POST'])
@login_required
def batch_process_serial_numbers():
    """批量处理串码（检测并录入）"""
    try:
        data = request.get_json()
        if not data or 'serial_numbers' not in data:
            return jsonify({'success': False, 'error': '缺少串码列表参数'}), 400

        serial_numbers = data['serial_numbers']
        notes = data.get('notes', '')
        created_by = current_user.username if current_user else '未知用户'

        if not isinstance(serial_numbers, list):
            return jsonify({'success': False, 'error': '串码列表格式错误'}), 400

        if len(serial_numbers) > 1000:
            return jsonify({'success': False, 'error': '一次最多只能处理1000个串码'}), 400

        # 导入串码服务V2（支持PostgreSQL）
        from app.services.serial_number_service_v2 import get_serial_service
        from app.services.serial_number_service import validate_serial_number
        service = get_serial_service()

        # 批量处理结果
        results = {
            'total': len(serial_numbers),
            'success': [],
            'duplicates': [],
            'invalid': [],
            'failed': []
        }

        for serial_number in serial_numbers:
            try:
                # 清理串码
                cleaned_serial = str(serial_number).strip()

                # 验证格式
                validation = validate_serial_number(cleaned_serial)
                if not validation['valid']:
                    results['invalid'].append({
                        'serial_number': cleaned_serial,
                        'error': validation['error']
                    })
                    continue

                # 尝试添加串码（内部会自动检查重复）
                add_result = service.add_serial_number(validation['cleaned_serial'], created_by, notes)

                if add_result['success']:
                    results['success'].append({
                        'serial_number': validation['cleaned_serial'],
                        'id': add_result.get('record_id'),
                        'created_at': add_result.get('created_at')
                    })
                else:
                    if add_result.get('error') == '已重复':
                        # 获取已存在记录的详细信息
                        existing_record = service.get_serial_number_info(validation['cleaned_serial'])
                        results['duplicates'].append({
                            'serial_number': validation['cleaned_serial'],
                            'error': add_result.get('message', '串码已存在'),
                            'existing_record': existing_record
                        })
                    else:
                        results['failed'].append({
                            'serial_number': validation['cleaned_serial'],
                            'error': add_result.get('message', '添加失败')
                        })

            except Exception as e:
                results['failed'].append({
                    'serial_number': str(serial_number).strip(),
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        logger.error(f"批量处理串码时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': f'服务器错误: {str(e)}'}), 500


# 串码统计信息API已移至main_refactored.py，避免重复定义
