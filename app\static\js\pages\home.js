/**
 * Home Page Controller - 首页控制器
 * 企业级模块化架构
 */

// 首页主控制器
class HomePageController {
    constructor() {
        this.modules = new Map();
        this.config = {
            enableLazyLoading: true,
            cacheEnabled: true,
            errorBoundary: true
        };
        this.init();
    }

    init() {
        this.setupErrorBoundary();
        this.initializeModules();
        this.bindEvents();
    }

    // 错误边界设置
    setupErrorBoundary() {
        if (!this.config.errorBoundary) return;
        
        window.addEventListener('error', this.handleGlobalError.bind(this));
        window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
    }

    handleGlobalError(event) {
        console.error('Global error caught:', event.error);
        this.showErrorNotification('页面发生错误，请刷新重试');
    }

    handlePromiseRejection(event) {
        console.error('Unhandled promise rejection:', event.reason);
        this.showErrorNotification('操作失败，请重试');
    }

    // 初始化所有模块
    initializeModules() {
        try {
            // 侧边栏模块
            this.initSidebarModule();
            
            // API状态检查模块
            this.initAPIStatusModule();
            
            // 快速搜索模块
            this.initQuickSearchModule();
            
            // 日历模块
            this.initCalendarModule();
            
            // 计算器模块
            this.initCalculatorModule();
            
            // 待办事项模块
            this.initTodoModule();
            
            // 二维码生成器模块
            this.initQRCodeModule();

            // 企业信用查询模块
            this.initEnterpriseCreditModule();

            // 串码查重模块
            this.initSerialNumberModule();

            // 公证接口模块
            this.initNotarizationModule();

        } catch (error) {
            console.error('Module initialization failed:', error);
            this.showErrorNotification('页面初始化失败');
        }
    }

    // 侧边栏模块初始化
    initSidebarModule() {
        if (typeof SidebarManager !== 'undefined') {
            const sidebarManager = new SidebarManager();
            sidebarManager.init();
            this.modules.set('sidebar', sidebarManager);
        }
    }

    // API状态检查模块
    initAPIStatusModule() {
        if (typeof AppNavigation !== 'undefined' && AppNavigation.checkApiStatus) {
            AppNavigation.checkApiStatus();
        }
    }

    // 快速搜索模块
    initQuickSearchModule() {
        const quickSearchForm = document.getElementById('quickSearchForm');
        if (quickSearchForm) {
            const quickSearch = new QuickSearchModule(quickSearchForm);
            this.modules.set('quickSearch', quickSearch);
        }
    }

    // 日历模块初始化
    initCalendarModule() {
        const calendarEl = document.getElementById('miniCalendar');
        if (calendarEl) {
            const calendar = new CalendarModule(calendarEl);
            this.modules.set('calendar', calendar);
        }
    }

    // 计算器模块初始化
    initCalculatorModule() {
        const calculatorLink = document.getElementById('calculatorLink');
        if (calculatorLink) {
            const calculator = new CalculatorModule(calculatorLink);
            this.modules.set('calculator', calculator);
        }
    }

    // 待办事项模块初始化
    initTodoModule() {
        const addTodoBtn = document.getElementById('addTodoBtn');
        if (addTodoBtn) {
            const todo = new TodoModule(addTodoBtn);
            this.modules.set('todo', todo);
        }
    }

    // 二维码生成器模块初始化
    initQRCodeModule() {
        const qrcodeLink = document.getElementById('qrcodeGeneratorLink');
        if (qrcodeLink) {
            const qrcode = new QRCodeModule(qrcodeLink);
            this.modules.set('qrcode', qrcode);
        }
    }

    // 企业信用查询模块初始化
    initEnterpriseCreditModule() {
        const enterpriseCreditLink = document.getElementById('enterpriseCreditLink');
        if (enterpriseCreditLink) {
            const enterpriseCredit = new EnterpriseCreditModule(enterpriseCreditLink);
            this.modules.set('enterpriseCredit', enterpriseCredit);
        }
    }

    // 串码查重模块初始化
    initSerialNumberModule() {
        const serialNumberLink = document.getElementById('serialNumberCheckLink');
        if (serialNumberLink) {
            // 等待全局组件加载完成
            const initSerialNumber = () => {
                if (window.SerialNumberModalModule) {
                    // 优先使用模态框组件
                    const serialNumberModal = new window.SerialNumberModalModule(serialNumberLink);
                    this.modules.set('serialNumber', serialNumberModal);
                } else if (window.SerialNumberChecker) {
                    // 使用全局串码查重器
                    window.initSerialNumberChecker();
                } else {
                    // 兼容旧的内联模块
                    const serialNumber = new SerialNumberModule(serialNumberLink);
                    this.modules.set('serialNumber', serialNumber);
                }
            };

            // 检查组件是否已加载，使用更可靠的加载检测
            if (!window.SerialNumberModalModule && !window.SerialNumberChecker) {
                // 使用MutationObserver等待组件脚本加载，或在DOMContentLoaded后重试
                const checkComponents = () => {
                    if (window.SerialNumberModalModule || window.SerialNumberChecker) {
                        initSerialNumber();
                    } else if (document.readyState === 'complete') {
                        // 如果页面已完全加载但组件仍未就绪，使用降级方案
                        initSerialNumber();
                    } else {
                        // 继续监听直到页面加载完成
                        requestAnimationFrame(checkComponents);
                    }
                };
                checkComponents();
            } else {
                initSerialNumber();
            }
        }
    }

    // 公证接口模块初始化
    initNotarizationModule() {
        const notarizationLink = document.getElementById('notarizationLink');
        if (notarizationLink) {
            const notarization = new NotarizationModule(notarizationLink);
            this.modules.set('notarization', notarization);
        }
    }

    // 绑定全局事件
    bindEvents() {
        // 注册表单提交处理器
        this.bindFormHandlers();
        
        // 绑定待办事项复选框
        this.bindTodoCheckboxes();
    }

    bindFormHandlers() {
        const dateFilterForm = document.getElementById('dateFilterForm');
        if (dateFilterForm) {
            dateFilterForm.addEventListener('submit', this.handleDateFilter.bind(this));
        }
        
        const customerSearchForm = document.getElementById('customerSearchForm');
        if (customerSearchForm) {
            customerSearchForm.addEventListener('submit', this.handleCustomerSearch.bind(this));
        }
    }

    bindTodoCheckboxes() {
        document.querySelectorAll('.todo-list .form-check-input').forEach(checkbox => {
            checkbox.addEventListener('change', this.handleTodoToggle.bind(this));
        });
    }

    // 事件处理器
    handleDateFilter(e) {
        e.preventDefault();
        if (typeof AppNavigation !== 'undefined' && AppNavigation.filterByDate) {
            AppNavigation.filterByDate();
        }
    }

    handleCustomerSearch(e) {
        e.preventDefault();
        if (typeof AppNavigation !== 'undefined' && AppNavigation.searchCustomer) {
            AppNavigation.searchCustomer();
        }
    }

    handleTodoToggle(e) {
        const todoItem = e.target.closest('.list-group-item');
        if (todoItem) {
            todoItem.style.opacity = e.target.checked ? '0.6' : '1';
            todoItem.classList.toggle('completed', e.target.checked);
        }
    }

    // 通用方法
    showErrorNotification(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    showSuccessNotification(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-success position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // 销毁方法
    destroy() {
        this.modules.forEach(module => {
            if (module.destroy) {
                module.destroy();
            }
        });
        this.modules.clear();
    }
}

// 快速搜索模块
class QuickSearchModule {
    constructor(formElement) {
        this.form = formElement;
        this.input = formElement.querySelector('#quickSearch');
        this.init();
    }

    init() {
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        this.input.addEventListener('input', this.handleInput.bind(this));
    }

    handleSubmit(e) {
        e.preventDefault();
        const searchValue = this.input.value.trim();
        if (searchValue) {
            window.location.href = `/?customerName=${encodeURIComponent(searchValue)}&tab=customer`;
        }
    }

    handleInput(e) {
        // 可以添加实时搜索建议功能
        const value = e.target.value.trim();
        if (value.length >= 2) {
            // 这里可以添加搜索建议逻辑
        }
    }
}

// 日历模块
class CalendarModule {
    constructor(container) {
        this.container = container;
        this.currentDate = new Date();
        this.events = [
            { date: 10, title: '系统维护', type: 'primary' },
            { date: 15, title: '团队会议', type: 'info' },
            { date: 30, title: '月度总结', type: 'success' }
        ];
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const today = new Date().getDate();
        
        const monthNames = [
            '一月', '二月', '三月', '四月', '五月', '六月',
            '七月', '八月', '九月', '十月', '十一月', '十二月'
        ];
        
        const dayNames = ['日', '一', '二', '三', '四', '五', '六'];
        
        const html = `
            <div class="simple-calendar">
                <div class="calendar-header d-flex justify-content-between align-items-center mb-3">
                    <button class="btn btn-sm btn-outline-primary" data-action="prev-month">&larr;</button>
                    <h6 class="mb-0">${year}年${monthNames[month]}</h6>
                    <button class="btn btn-sm btn-outline-primary" data-action="next-month">&rarr;</button>
                </div>
                <div class="calendar-grid">
                    <div class="week-header">
                        ${dayNames.map(day => `<div class="day-header">${day}</div>`).join('')}
                    </div>
                    <div class="days-grid">
                        ${this.generateCalendarDays(year, month, today)}
                    </div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
    }

    generateCalendarDays(year, month, today) {
        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        let days = '';
        
        // 空白日期
        for (let i = 0; i < firstDay; i++) {
            days += '<div class="calendar-day empty"></div>';
        }
        
        // 实际日期
        for (let day = 1; day <= daysInMonth; day++) {
            const event = this.events.find(e => e.date === day);
            const isToday = day === today && 
                           month === new Date().getMonth() && 
                           year === new Date().getFullYear();
            const classes = ['calendar-day'];
            
            if (isToday) classes.push('today');
            if (event) classes.push('has-event');
            
            days += `
                <div class="${classes.join(' ')}" title="${event ? event.title : ''}" tabindex="0">
                    <span class="day-number">${day}</span>
                    ${event ? `<div class="event-dot bg-${event.type}"></div>` : ''}
                </div>
            `;
        }
        
        return days;
    }

    bindEvents() {
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action === 'prev-month') {
                this.changeMonth(-1);
            } else if (action === 'next-month') {
                this.changeMonth(1);
            }
        });
    }

    changeMonth(direction) {
        this.currentDate.setMonth(this.currentDate.getMonth() + direction);
        this.render();
        this.bindEvents();
    }
}

// 串码查重模块
class SerialNumberModule {
    constructor(linkElement) {
        this.link = linkElement;
        this.modal = null;
        this.init();
    }

    init() {
        this.link.addEventListener('click', this.handleClick.bind(this));
        this.modal = document.getElementById('serialNumberModal');
    }

    handleClick(e) {
        e.preventDefault();
        if (this.modal) {
            const modalInstance = new bootstrap.Modal(this.modal);
            modalInstance.show();
        }
    }
}

// 公证接口模块
class NotarizationModule {
    constructor(linkElement) {
        this.link = linkElement;
        this.init();
    }

    init() {
        this.link.addEventListener('click', this.handleClick.bind(this));
    }

    handleClick(e) {
        e.preventDefault();
        // 确保公证模态框组件已加载
        if (window.notarizationModal) {
            window.notarizationModal.show();
        } else {
            console.warn('公证模态框组件未加载');
            // 可以显示一个提示或尝试重新加载组件
            alert('公证接口功能正在加载中，请稍后再试');
        }
    }
}

// 导出模块（如果需要）
window.HomePageController = HomePageController;
window.QuickSearchModule = QuickSearchModule;
window.CalendarModule = CalendarModule;
window.SerialNumberModule = SerialNumberModule;
window.NotarizationModule = NotarizationModule;

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    window.homePageController = new HomePageController();
});