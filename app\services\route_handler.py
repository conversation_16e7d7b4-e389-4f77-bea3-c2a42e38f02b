"""
路由处理器服务 - 将复杂的路由逻辑从控制器中分离出来
"""
from typing import Dict, Any, Optional, Tuple
from flask import request, render_template, redirect, url_for, flash
from flask_login import current_user
import datetime
import logging

from app.services.data_service import (
    get_filtered_data,
    get_orders_by_customer,
)
from app.services.home_service import get_home_stats_cached
from config import Config

logger = logging.getLogger(__name__)


class RouteHandler:
    """路由处理器 - 统一处理路由逻辑"""
    
    def __init__(self):
        self.today = datetime.date.today().strftime('%Y-%m-%d')
    
    def handle_index_request(self) -> Any:
        """处理主页请求"""
        try:
            # 获取URL参数
            params = self._extract_url_params()
            
            # 根据参数类型路由到不同处理方法
            if params['tab'] == 'filter' and params['date']:
                return self._handle_filter_request(params['date'])
            elif params['tab'] == 'customer' and params['customer_name']:
                return self._handle_customer_request(params['customer_name'])
            
            # 默认重定向到工作台
            return redirect(url_for('main.homepage'))
            
        except Exception as e:
            logger.error(f"处理主页请求失败: {str(e)}")
            flash('页面加载失败，请重试', 'error')
            return redirect(url_for('main.homepage'))
    
    def handle_homepage_request(self) -> Any:
        """处理工作台首页请求"""
        try:
            # 获取统计数据（仅读缓存，零阻塞）
            stats = self._get_homepage_stats()
            
            return render_template(
                'home.html', 
                user=current_user, 
                version=Config.VERSION,
                today=self.today,
                stats=stats
            )
            
        except Exception as e:
            logger.error(f"处理工作台请求失败: {str(e)}")
            # 返回基础页面，避免完全失败
            return render_template(
                'home.html', 
                user=current_user, 
                version=Config.VERSION,
                today=self.today,
                stats=self._get_default_stats()
            )
    
    def _extract_url_params(self) -> Dict[str, Optional[str]]:
        """提取URL参数"""
        return {
            'date': request.args.get('date'),
            'customer_name': request.args.get('customerName'),
            'tab': request.args.get('tab')
        }
    
    def _handle_filter_request(self, date: str) -> Any:
        """处理日期筛选请求"""
        logger.info(f"处理日期筛选请求: {date}")
        
        # 验证日期格式
        if not self._validate_date_format(date):
            flash('日期格式不正确', 'error')
            return redirect(url_for('main.homepage'))
        
        # 获取筛选数据
        results = get_filtered_data(date)
        
        # 标准化数据格式
        standardized_results = self._standardize_data_format(results)
        
        return render_template(
            'dashboard.html',
            user=current_user,
            filter_results=standardized_results,
            selected_date=date,
            tab='filter',
            today=self.today,
            version=Config.VERSION
        )
    
    def _handle_customer_request(self, customer_name: str) -> Any:
        """处理客户查询请求"""
        logger.info(f"处理客户查询请求: {customer_name}")
        
        # 验证客户名称
        if not self._validate_customer_name(customer_name):
            flash('客户名称不能为空', 'error')
            return redirect(url_for('main.homepage'))
        
        # 获取客户数据
        results = get_orders_by_customer(customer_name)
        
        # 标准化数据格式
        standardized_results = self._standardize_data_format(results)
        
        return render_template(
            'dashboard.html',
            user=current_user,
            customer_results=standardized_results,
            customer_name=customer_name,
            tab='customer',
            today=self.today,
            version=Config.VERSION
        )
    
    def _get_homepage_stats(self) -> Dict[str, Any]:
        """获取首页统计（只读缓存，不触发任何上游请求）"""
        return get_home_stats_cached()
    
    def _get_default_stats(self) -> Dict[str, Any]:
        """获取默认统计数据"""
        return {
            'today_orders': 0,
            'overdue_orders': 0,
            'system_status': 'unknown',
            'api_status': 'unknown'
        }
    
    def _standardize_data_format(self, data: Any) -> Dict[str, Any]:
        """标准化数据格式"""
        if isinstance(data, dict) and 'error' in data:
            return data
        
        if isinstance(data, dict) and 'results' in data:
            # 确保有列定义
            if 'columns' not in data and data['results']:
                if isinstance(data['results'][0], dict):
                    data['columns'] = list(data['results'][0].keys())
                else:
                    data['columns'] = ['数据']
            return data
        
        # 处理其他格式
        if isinstance(data, list):
            columns = []
            if data and isinstance(data[0], dict):
                columns = list(data[0].keys())
            else:
                columns = ['数据']
            return {'results': data, 'columns': columns}
        
        return {'results': [], 'columns': [], 'error': '数据格式不正确'}
    
    def _validate_date_format(self, date_str: str) -> bool:
        """验证日期格式"""
        try:
            datetime.datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def _validate_customer_name(self, name: str) -> bool:
        """验证客户名称"""
        return bool(name and name.strip())


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_export_data(data: Any) -> Tuple[bool, str]:
        """验证导出数据"""
        if not data:
            return False, "没有数据可导出"
        
        if not isinstance(data, list):
            return False, "数据格式不正确"
        
        if len(data) == 0:
            return False, "数据为空"
        
        return True, ""
    
    @staticmethod
    def validate_file_upload(file) -> Tuple[bool, str]:
        """验证文件上传"""
        if not file:
            return False, "没有选择文件"
        
        if file.filename == '':
            return False, "文件名为空"
        
        # 检查文件扩展名
        allowed_extensions = {'.xlsx', '.xls', '.csv'}
        file_ext = file.filename.lower().split('.')[-1]
        if f'.{file_ext}' not in allowed_extensions:
            return False, f"不支持的文件格式: {file_ext}"
        
        return True, ""


class ResponseFormatter:
    """响应格式化器"""
    
    @staticmethod
    def format_success_response(data: Any, message: str = "") -> Dict[str, Any]:
        """格式化成功响应"""
        return {
            'success': True,
            'data': data,
            'message': message,
            'timestamp': datetime.datetime.now().isoformat()
        }
    
    @staticmethod
    def format_error_response(error: str, code: int = 400) -> Tuple[Dict[str, Any], int]:
        """格式化错误响应"""
        return {
            'success': False,
            'error': error,
            'timestamp': datetime.datetime.now().isoformat()
        }, code 
