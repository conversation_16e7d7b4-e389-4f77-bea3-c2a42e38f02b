/**
 * 统一串码录入功能模块
 * 支持单条和批量录入的统一处理
 */

class UnifiedSerialNumberProcessor {
    constructor() {
        // 统一录入元素
        this.modal = null;
        this.form = null;
        this.input = null;
        this.notesInput = null;
        this.processBtn = null;
        this.clearBtn = null;
        this.resultDiv = null;
        this.alertDiv = null;
        this.messageDiv = null;
        this.loadingDiv = null;
        this.progressDiv = null;
        this.progressBar = null;
        this.progressText = null;

        // 统计信息元素
        this.totalCountEl = null;
        this.todayCountEl = null;
        this.weekCountEl = null;

        this.init();
    }
    
    init() {
        // 获取DOM元素
        this.modal = document.getElementById('serialNumberModal');
        this.form = document.getElementById('unifiedSerialNumberForm');
        this.input = document.getElementById('unifiedSerialNumberInput');
        this.notesInput = document.getElementById('unifiedSerialNumberNotes');
        this.processBtn = document.getElementById('processSerialNumberBtn');
        this.clearBtn = document.getElementById('clearUnifiedSerialNumberBtn');
        this.resultDiv = document.getElementById('unifiedSerialNumberResult');
        this.alertDiv = document.getElementById('unifiedSerialNumberAlert');
        this.messageDiv = document.getElementById('unifiedSerialNumberMessage');
        this.loadingDiv = document.getElementById('unifiedSerialNumberLoading');
        this.progressDiv = document.getElementById('unifiedSerialNumberProgress');
        this.progressBar = document.getElementById('unifiedProgressBar');
        this.progressText = document.getElementById('unifiedProgressText');

        // 获取统计信息元素
        this.totalCountEl = document.getElementById('totalSerialCount');
        this.todayCountEl = document.getElementById('todaySerialCount');
        this.weekCountEl = document.getElementById('weekSerialCount');

        this.bindEvents();
        this.loadStatistics();
    }

    bindEvents() {
        // 绑定输入事件
        if (this.input) {
            this.input.addEventListener('input', () => this.validateInput());
            this.input.addEventListener('paste', () => {
                // 延迟执行以确保粘贴内容已经插入
                setTimeout(() => this.validateInput(), 10);
            });
        }

        // 绑定按钮事件
        if (this.processBtn) {
            this.processBtn.addEventListener('click', () => this.handleProcess());
        }
        
        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => this.clearForm());
        }

        // 绑定表单提交事件
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleProcess();
            });
        }
    }

    // 输入验证
    validateInput() {
        if (!this.input) return false;
        
        const text = this.input.value.trim();
        const lines = text.split('\n').filter(line => line.trim());
        
        // 更新按钮状态
        if (this.processBtn) {
            this.processBtn.disabled = lines.length === 0;
        }
        
        return lines.length > 0;
    }

    // 解析输入内容
    parseInput() {
        if (!this.input) return [];
        
        const text = this.input.value.trim();
        return text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
    }

    // 检测是单条还是批量
    detectProcessType(serialNumbers) {
        return serialNumbers.length === 1 ? 'single' : 'batch';
    }

    // 统一处理入口
    async handleProcess() {
        const serialNumbers = this.parseInput();
        
        if (serialNumbers.length === 0) {
            this.showError('请输入至少一个串码');
            return;
        }
        
        if (serialNumbers.length > 1000) {
            this.showError('一次最多只能处理1000个串码');
            return;
        }

        const processType = this.detectProcessType(serialNumbers);
        const notes = this.notesInput ? this.notesInput.value.trim() : '';
        
        this.showLoading(true);
        this.hideResult();
        
        if (processType === 'batch') {
            this.showProgress(0, serialNumbers.length);
        }
        
        try {
            const response = await fetch('/api/serial-number/batch-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    serial_numbers: serialNumbers,
                    notes: notes
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayResults(result.results, processType);
                
                // 更新统计信息
                this.updateStatistics();
                
                // 清空表单
                this.clearForm(false);
                
                // 5秒后自动隐藏结果
                setTimeout(() => {
                    this.hideResult();
                }, 5000);
            } else {
                this.showError(result.error || '处理失败');
            }
        } catch (error) {
            console.error('处理错误:', error);
            this.showError('网络错误，请稍后重试');
        } finally {
            this.showLoading(false);
            this.hideProgress();
        }
    }

    // 显示处理结果
    displayResults(results, processType) {
        const isMultiple = processType === 'batch';
        let title = isMultiple ? '批量录入结果' : '录入结果';
        
        const summary = this.createResultSummary(results, title, isMultiple);
        
        let content = summary;
        
        // 显示成功录入的时间戳
        if (results.success.length > 0) {
            const timestamp = new Date().toLocaleString('zh-CN');
            content += `<div class="mt-2 small text-success">录入时间: ${timestamp}</div>`;
        }
        
        // 显示详细信息
        content += this.createDetailedResults(results, isMultiple);
        
        this.showSuccess(title, content);
    }

    // 创建结果汇总
    createResultSummary(results, title, isMultiple) {
        let content = `<div class="result-summary">`;
        content += `<h6 class="mb-3">${title}</h6>`;
        
        if (isMultiple) {
            // 批量处理显示详细统计
            content += `<div class="result-item">`;
            content += `<span class="result-label">总数:</span>`;
            content += `<span class="result-count info">${results.total}</span>`;
            content += `</div>`;
        }
        
        // 成功录入
        if (results.success && results.success.length > 0) {
            content += `<div class="result-item">`;
            content += `<span class="result-label">成功录入:</span>`;
            content += `<span class="result-count success">${results.success.length}</span>`;
            content += `</div>`;
        }
        
        // 重复跳过
        if (results.duplicates && results.duplicates.length > 0) {
            content += `<div class="result-item">`;
            content += `<span class="result-label">重复跳过:</span>`;
            content += `<span class="result-count warning">${results.duplicates.length}</span>`;
            content += `</div>`;
        }
        
        // 无效串码
        if (results.invalid && results.invalid.length > 0) {
            content += `<div class="result-item">`;
            content += `<span class="result-label">无效串码:</span>`;
            content += `<span class="result-count danger">${results.invalid.length}</span>`;
            content += `</div>`;
        }
        
        // 录入失败
        if (results.failed && results.failed.length > 0) {
            content += `<div class="result-item">`;
            content += `<span class="result-label">录入失败:</span>`;
            content += `<span class="result-count danger">${results.failed.length}</span>`;
            content += `</div>`;
        }
        
        content += `</div>`;
        return content;
    }

    // 创建详细结果
    createDetailedResults(results, isMultiple) {
        let content = '';
        
        // 显示重复详情
        if (results.duplicates && results.duplicates.length > 0) {
            content += '<div class="duplicate-warning">';
            content += '<h6><i class="bi bi-exclamation-triangle me-2"></i>重复串码检测</h6>';
            const showCount = isMultiple ? 10 : 5;
            results.duplicates.slice(0, showCount).forEach(item => {
                const record = item.existing_record;
                const dateStr = record ? new Date(record.created_at).toLocaleString('zh-CN') : '未知时间';
                content += `<div class="duplicate-item">`;
                content += `<span class="duplicate-serial">${item.serial_number}</span>`;
                content += `<br><small class="duplicate-date">已于 ${dateStr} 录入</small>`;
                content += `</div>`;
            });
            if (results.duplicates.length > showCount) {
                content += `<div class="small text-muted mt-2">... 还有${results.duplicates.length - showCount}个重复串码</div>`;
            }
            content += '</div>';
        }
        
        // 显示无效详情
        if (results.invalid && results.invalid.length > 0) {
            content += '<div class="mt-3"><h6 class="text-danger">无效串码:</h6>';
            content += '<div class="small text-muted">';
            const showCount = isMultiple ? 10 : 5;
            results.invalid.slice(0, showCount).forEach(item => {
                content += `<div>• ${item.serial_number}: ${item.error}</div>`;
            });
            if (results.invalid.length > showCount) {
                content += `<div>... 还有${results.invalid.length - showCount}个无效串码</div>`;
            }
            content += '</div></div>';
        }
        
        // 显示失败详情
        if (results.failed && results.failed.length > 0) {
            content += '<div class="mt-3"><h6 class="text-danger">录入失败:</h6>';
            content += '<div class="small text-muted">';
            const showCount = isMultiple ? 5 : 3;
            results.failed.slice(0, showCount).forEach(item => {
                content += `<div>• ${item.serial_number}: ${item.error}</div>`;
            });
            if (results.failed.length > showCount) {
                content += `<div>... 还有${results.failed.length - showCount}个失败记录</div>`;
            }
            content += '</div></div>';
        }
        
        return content;
    }

    // 辅助方法
    showLoading(show) {
        if (this.loadingDiv) {
            this.loadingDiv.style.display = show ? 'block' : 'none';
        }

        // 禁用/启用按钮
        if (this.processBtn) this.processBtn.disabled = show;
        if (this.clearBtn) this.clearBtn.disabled = show;
    }

    showProgress(current, total) {
        if (this.progressDiv) {
            this.progressDiv.style.display = 'block';
        }

        if (this.progressBar) {
            const percentage = total > 0 ? (current / total) * 100 : 0;
            this.progressBar.style.width = `${percentage}%`;
            this.progressBar.setAttribute('aria-valuenow', percentage);
        }

        if (this.progressText) {
            this.progressText.textContent = `${current}/${total}`;
        }
    }

    hideProgress() {
        if (this.progressDiv) {
            this.progressDiv.style.display = 'none';
        }
    }

    showSuccess(title, content) {
        if (this.alertDiv && this.messageDiv) {
            this.alertDiv.className = 'alert alert-success';
            this.messageDiv.innerHTML = content;
            this.resultDiv.style.display = 'block';
        }
    }

    showError(message) {
        if (this.alertDiv && this.messageDiv) {
            this.alertDiv.className = 'alert alert-danger';
            this.messageDiv.innerHTML = `<div><strong>错误:</strong> ${message}</div>`;
            this.resultDiv.style.display = 'block';
        }
    }

    hideResult() {
        if (this.resultDiv) {
            this.resultDiv.style.display = 'none';
        }
    }

    clearForm(hideResult = true) {
        if (this.input) {
            this.input.value = '';
        }
        if (this.notesInput) {
            this.notesInput.value = '';
        }

        // 重置按钮状态
        if (this.processBtn) this.processBtn.disabled = true;

        // 只有在指定时才隐藏结果
        if (hideResult) {
            this.hideResult();
        }

        this.hideProgress();
    }

    // 加载统计信息
    async loadStatistics() {
        try {
            const response = await fetch('/api/serial-number/statistics');

            if (!response.ok) {
                console.error('API响应错误:', response.status, response.statusText);
                return;
            }

            const data = await response.json();

            if (data.success) {
                this.updateStatisticsDisplay(data.statistics);
            } else {
                console.error('API返回失败:', data.error);
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    // 更新统计信息
    async updateStatistics() {
        await this.loadStatistics();
    }

    // 更新统计信息显示
    updateStatisticsDisplay(stats) {
        if (this.totalCountEl) {
            this.animateNumber(this.totalCountEl, stats.total_count);
        }
        if (this.todayCountEl) {
            this.animateNumber(this.todayCountEl, stats.today_count);
        }
        if (this.weekCountEl) {
            this.animateNumber(this.weekCountEl, stats.week_count);
        }
    }

    // 数字动画效果
    animateNumber(element, targetNumber) {
        const currentNumber = parseInt(element.textContent) || 0;
        const increment = Math.ceil((targetNumber - currentNumber) / 20);

        if (increment === 0) return;

        const timer = setInterval(() => {
            const current = parseInt(element.textContent) || 0;
            const next = current + increment;

            if ((increment > 0 && next >= targetNumber) || (increment < 0 && next <= targetNumber)) {
                element.textContent = targetNumber;
                clearInterval(timer);
            } else {
                element.textContent = next;
            }
        }, 50);
    }
}

// 初始化组件
document.addEventListener('DOMContentLoaded', function() {
    window.unifiedSerialNumberProcessor = new UnifiedSerialNumberProcessor();
});
