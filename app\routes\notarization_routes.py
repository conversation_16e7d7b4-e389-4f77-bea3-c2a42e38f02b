"""
公证接口路由
提供公证相关功能的REST API端点
"""

from flask import Blueprint, request, jsonify, render_template
from flask_login import login_required, current_user
import logging

from app.services.notarization_service import get_notarization_service

logger = logging.getLogger(__name__)

# 创建公证蓝图
notarization_bp = Blueprint('notarization', __name__)


@notarization_bp.route('/api/notarization/settle-order', methods=['POST'])
@login_required
def settle_order():
    """结清公证订单API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400
        
        order_id = data.get('order_id', '').strip()
        if not order_id:
            return jsonify({
                'success': False,
                'error': '订单号不能为空'
            }), 400
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求结清订单: {order_id}")
        
        # 调用服务
        service = get_notarization_service()
        result = service.settle_order(order_id)
        
        # 记录结果
        if result.get('success'):
            logger.info(f"订单结清成功: {order_id}")
        else:
            logger.warning(f"订单结清失败: {order_id}, 原因: {result.get('error')}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"结清订单API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500


@notarization_bp.route('/api/notarization/query-orders', methods=['POST'])
@login_required
def query_user_orders():
    """查询用户有效订单API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400
        
        id_card = data.get('id_card', '').strip()
        if not id_card:
            return jsonify({
                'success': False,
                'error': '身份证号不能为空'
            }), 400
        
        # 记录操作日志（添加请求ID以便跟踪）
        import uuid
        request_id = str(uuid.uuid4())[:8]
        logger.info(f"[{request_id}] 用户 {current_user.username} 查询订单，身份证: {id_card[:6]}****")
        
        # 调用服务
        service = get_notarization_service()
        result = service.query_user_orders(id_card)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"查询订单API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500


@notarization_bp.route('/api/notarization/query-imei', methods=['POST'])
@login_required
def query_imei_validity():
    """查询IMEI编码有效性API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400
        
        imei_input = data.get('imei_codes', '').strip()
        if not imei_input:
            return jsonify({
                'success': False,
                'error': 'IMEI编码不能为空'
            }), 400
        
        # 解析IMEI列表
        from app.services.notarization_service import NotarizationApiUtils
        valid_imeis, invalid_imeis = NotarizationApiUtils.parse_imei_list(imei_input)
        
        if not valid_imeis:
            return jsonify({
                'success': False,
                'error': '没有有效的IMEI编码',
                'invalid_imeis': invalid_imeis
            }), 400
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 查询IMEI有效性，数量: {len(valid_imeis)}")
        
        # 调用服务
        service = get_notarization_service()
        result = service.query_imei_validity(valid_imeis)
        
        # 添加无效IMEI信息
        if invalid_imeis:
            result['invalid_imeis'] = invalid_imeis
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"查询IMEI API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500


@notarization_bp.route('/api/notarization/push-imei', methods=['POST'])
@login_required
def push_imei_code():
    """推送设备IMEI编码API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400
        
        order_id = data.get('order_id', '').strip()
        imei_code = data.get('imei_code', '').strip()
        raw_second_hand = data.get('is_second_hand', '2')
        is_second_hand = str(raw_second_hand).strip() if raw_second_hand is not None else '2'

        if not order_id:
            return jsonify({
                'success': False,
                'error': '订单号不能为空'
            }), 400

        if not imei_code:
            return jsonify({
                'success': False,
                'error': 'IMEI编码不能为空'
            }), 400

        # 记录操作日志
        logger.info(f"用户 {current_user.username} 推送IMEI: {order_id}, {imei_code}, 二手机状态: {is_second_hand}")

        # 调用服务
        service = get_notarization_service()
        result = service.push_imei_code(order_id, imei_code, is_second_hand)
        
        # 记录结果
        if result.get('success'):
            logger.info(f"IMEI推送成功: {order_id}, {imei_code}")
        else:
            logger.warning(f"IMEI推送失败: {order_id}, 原因: {result.get('error')}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"推送IMEI API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500


@notarization_bp.route('/api/notarization/validate-imei', methods=['POST'])
@login_required
def validate_imei_format():
    """验证IMEI格式API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400
        
        imei_input = data.get('imei_codes', '').strip()
        if not imei_input:
            return jsonify({
                'success': False,
                'error': 'IMEI编码不能为空'
            }), 400
        
        # 解析和验证IMEI
        from app.services.notarization_service import NotarizationApiUtils
        valid_imeis, invalid_imeis = NotarizationApiUtils.parse_imei_list(imei_input)
        
        return jsonify({
            'success': True,
            'data': {
                'valid_imeis': valid_imeis,
                'invalid_imeis': invalid_imeis,
                'total_count': len(valid_imeis) + len(invalid_imeis),
                'valid_count': len(valid_imeis),
                'invalid_count': len(invalid_imeis)
            }
        })
        
    except Exception as e:
        logger.error(f"验证IMEI格式API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500


@notarization_bp.route('/api/notarization/validate-id-card', methods=['POST'])
@login_required
def validate_id_card_format():
    """验证身份证号格式API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400
        
        id_card = data.get('id_card', '').strip()
        if not id_card:
            return jsonify({
                'success': False,
                'error': '身份证号不能为空'
            }), 400
        
        # 验证身份证格式
        from app.services.notarization_service import NotarizationApiUtils
        is_valid = NotarizationApiUtils.validate_id_card(id_card)
        
        return jsonify({
            'success': True,
            'data': {
                'id_card': id_card,
                'is_valid': is_valid,
                'message': '身份证号格式正确' if is_valid else '身份证号格式不正确'
            }
        })
        
    except Exception as e:
        logger.error(f"验证身份证格式API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500


@notarization_bp.route('/api/notarization/status', methods=['GET'])
@login_required
def get_notarization_status():
    """获取公证服务状态API"""
    try:
        # 检查用户权限
        service = get_notarization_service()
        
        return jsonify({
            'success': True,
            'data': {
                'service_available': True,
                'user_permissions': {
                    'limited': service.check_user_permission('limited'),
                    'standard': service.check_user_permission('standard'),
                    'full': service.check_user_permission('full')
                },
                'current_user_level': current_user.user_level if current_user.is_authenticated else None
            }
        })
        
    except Exception as e:
        logger.error(f"获取公证服务状态API异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '系统错误',
            'message': str(e)
        }), 500
