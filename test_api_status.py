#!/usr/bin/env python3
"""
API Status Testing Script
测试API状态检测功能的脚本

这个脚本可以用来测试API状态检测是否正常工作：
1. 测试正常状态（外部API可达）
2. 测试降级状态（外部API不可达但本地服务正常）
3. 模拟网络中断情况

使用方法：
python test_api_status.py [--block-external]
"""

import requests
import json
import time
import argparse
import sys
from datetime import datetime

def test_health_endpoint(base_url="http://localhost:5000"):
    """测试健康检查端点"""
    try:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 测试健康检查端点...")
        
        response = requests.get(f"{base_url}/api/health", timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            message = data.get('message', 'No message')
            
            print(f"✓ 健康检查成功")
            print(f"  状态: {status}")
            print(f"  消息: {message}")
            
            # 显示详细信息
            details = data.get('details', {})
            if 'local' in details:
                local = details['local']
                print(f"  本地服务: {local.get('status')} - {local.get('message')}")
            
            if 'external_api' in details:
                external = details['external_api']
                print(f"  外部API: {external.get('status')} - {external.get('message')}")
                if 'response_time' in external:
                    print(f"  响应时间: {external['response_time']:.3f}秒")
            
            return status
        else:
            print(f"✗ 健康检查失败: HTTP {response.status_code}")
            return "error"
            
    except requests.exceptions.Timeout:
        print("✗ 健康检查超时")
        return "timeout"
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到本地服务")
        return "connection_error"
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return "exception"

def test_ping_endpoint(base_url="http://localhost:5000"):
    """测试ping端点"""
    try:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 测试ping端点...")
        
        response = requests.get(f"{base_url}/api/ping", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            message = data.get('message', 'No message')
            
            print(f"✓ Ping成功")
            print(f"  状态: {status}")
            print(f"  消息: {message}")
            return status
        else:
            print(f"✗ Ping失败: HTTP {response.status_code}")
            return "error"
            
    except Exception as e:
        print(f"✗ Ping异常: {e}")
        return "exception"

def monitor_status(base_url="http://localhost:5000", interval=10, duration=60):
    """持续监控API状态"""
    print(f"开始监控API状态，间隔{interval}秒，持续{duration}秒...")
    print("按Ctrl+C停止监控")
    
    start_time = time.time()
    try:
        while time.time() - start_time < duration:
            print("\n" + "="*50)
            health_status = test_health_endpoint(base_url)
            print("-" * 30)
            ping_status = test_ping_endpoint(base_url)
            
            print(f"\n状态总结: 健康检查={health_status}, Ping={ping_status}")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    parser = argparse.ArgumentParser(description='API状态检测测试工具')
    parser.add_argument('--base-url', default='http://localhost:5000', 
                       help='本地服务基础URL (默认: http://localhost:5000)')
    parser.add_argument('--monitor', action='store_true', 
                       help='持续监控模式')
    parser.add_argument('--interval', type=int, default=10, 
                       help='监控间隔秒数 (默认: 10)')
    parser.add_argument('--duration', type=int, default=60, 
                       help='监控持续时间秒数 (默认: 60)')
    
    args = parser.parse_args()
    
    print("API状态检测测试工具")
    print("="*50)
    print(f"目标服务: {args.base_url}")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if args.monitor:
        monitor_status(args.base_url, args.interval, args.duration)
    else:
        # 单次测试
        test_health_endpoint(args.base_url)
        print()
        test_ping_endpoint(args.base_url)
        
        print("\n测试完成！")
        print("\n使用说明:")
        print("- 'ok' 状态表示外部API服务正常")
        print("- 'degraded' 状态表示本地服务正常但外部API异常")
        print("- 'error' 状态表示服务异常")
        print("\n要持续监控，请使用: python test_api_status.py --monitor")

if __name__ == "__main__":
    main()
