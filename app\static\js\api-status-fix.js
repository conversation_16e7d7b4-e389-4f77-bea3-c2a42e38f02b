/**
 * API状态检查修复器
 * 解决侧边栏API状态一直显示错误的问题
 */
class ApiStatusManager {
    constructor() {
        this.statusElement = null;
        this.checkInterval = null;
        this.isChecking = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.checkIntervalMs = 30000; // 30秒检查一次，更频繁的检查
        this.manualCheckCooldown = 3000; // 手动检查冷却时间3秒
        this.lastManualCheck = 0;
        this.lastSuccessfulCheck = 0;
        this.consecutiveFailures = 0;
    }

    init() {
        this.statusElement = document.getElementById('apiStatus');
        if (!this.statusElement) {
            console.warn('未找到API状态元素');
            return;
        }

        // 添加样式
        this.addStyles();

        // 绑定点击事件
        this.statusElement.addEventListener('click', () => {
            this.manualCheck();
        });

        // 执行首次检查
        this.checkStatus();

        // 设置定时检查
        this.startPeriodicCheck();

        console.log('API状态管理器已初始化');
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .api-status-container {
                margin-top: auto;
                padding: 10px 15px;
                font-size: 0.85rem;
            }
            
            .api-status-badge {
                display: inline-flex;
                align-items: center;
                gap: 5px;
                padding: 5px 8px;
                border-radius: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .api-status-badge:hover {
                transform: scale(1.05);
            }
            
            .status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
                animation: pulse 2s infinite;
            }
            
            .api-online .status-indicator {
                background-color: #28a745;
                box-shadow: 0 0 5px #28a745;
            }

            .api-degraded .status-indicator {
                background-color: #ffc107;
                box-shadow: 0 0 5px #ffc107;
            }

            .api-offline .status-indicator {
                background-color: #dc3545;
                box-shadow: 0 0 5px #dc3545;
            }

            .api-checking .status-indicator {
                background-color: #6c757d;
                box-shadow: 0 0 5px #6c757d;
            }

            .api-unknown .status-indicator {
                background-color: #ffc107;
                box-shadow: 0 0 5px #ffc107;
            }
            
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    async checkStatus() {
        if (this.isChecking) return;

        this.isChecking = true;
        this.updateUI('checking', '检测中...');

        try {
            // 使用新的健康检查端点，检查实际的外部API状态
            const healthResult = await this.checkHealthEndpoint('/api/health');

            if (healthResult.success) {
                const data = healthResult.data;

                if (data.status === 'ok') {
                    this.updateUI('online', '正常');
                    this.retryCount = 0;
                    this.consecutiveFailures = 0;
                    this.lastSuccessfulCheck = Date.now();
                    console.log('API状态检查: 外部API正常');
                } else if (data.status === 'degraded') {
                    this.updateUI('degraded', '降级');
                    this.retryCount = 0;
                    this.consecutiveFailures = 0;
                    this.lastSuccessfulCheck = Date.now();
                    console.log('API状态检查: 本地服务正常，外部API异常');
                } else {
                    this.updateUI('offline', '异常');
                    this.retryCount = 0;
                    this.consecutiveFailures++;
                    console.log('API状态检查: 服务异常');
                }
            } else {
                // 健康检查端点失败，降级到简单ping检查
                console.warn('健康检查端点失败，降级到ping检查');
                const pingResult = await this.pingEndpoint('/api/ping');

                if (pingResult) {
                    this.updateUI('degraded', '降级');
                    this.retryCount = 0;
                    this.consecutiveFailures++;
                    console.log('API状态检查: 本地服务正常，外部API状态未知');
                } else {
                    throw new Error('所有检查都失败');
                }
            }

        } catch (error) {
            console.error('API状态检查失败:', error);
            this.retryCount++;
            this.consecutiveFailures++;

            if (this.retryCount <= this.maxRetries) {
                // 重试，使用递增延迟
                const retryDelay = Math.min(2000 * this.retryCount, 10000); // 最大10秒延迟
                setTimeout(() => {
                    this.isChecking = false;
                    this.checkStatus();
                }, retryDelay);
            } else {
                this.updateUI('offline', '离线');
                this.retryCount = 0;

                // 根据连续失败次数调整下次检查间隔
                const nextCheckDelay = Math.min(this.checkIntervalMs * Math.pow(1.5, this.consecutiveFailures), 300000); // 最大5分钟
                console.log(`API连续失败${this.consecutiveFailures}次，${nextCheckDelay/1000}秒后重新检查`);
                setTimeout(() => this.checkStatus(), nextCheckDelay);
            }
        } finally {
            if (this.retryCount === 0) {
                this.isChecking = false;
            }
        }
    }

    async checkHealthEndpoint(endpoint) {
        try {
            // 使用AbortController来实现超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时，给外部API检查足够时间

            const response = await fetch(endpoint, {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const data = await response.json();
                return { success: true, data: data };
            } else {
                console.debug(`健康检查端点 ${endpoint} 返回状态码: ${response.status}`);
                return { success: false, error: `HTTP ${response.status}` };
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.debug(`健康检查端点 ${endpoint} 超时`);
                return { success: false, error: '超时' };
            } else {
                console.debug(`健康检查端点 ${endpoint} 失败:`, error.message);
                return { success: false, error: error.message };
            }
        }
    }

    async pingEndpoint(endpoint) {
        try {
            // 使用AbortController来实现超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

            const response = await fetch(endpoint, {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            return response.ok;
        } catch (error) {
            if (error.name === 'AbortError') {
                console.debug(`端点 ${endpoint} 检查超时`);
            } else {
                console.debug(`端点 ${endpoint} 检查失败:`, error.message);
            }
            return false;
        }
    }

    updateUI(status, message) {
        if (!this.statusElement) return;

        let badgeClass, badgeColor, title;

        switch (status) {
            case 'online':
                badgeClass = 'api-online';
                badgeColor = 'bg-success';
                title = '外部API服务正常 - 点击手动检查';
                break;
            case 'degraded':
                badgeClass = 'api-degraded';
                badgeColor = 'bg-warning';
                title = '本地服务正常，外部API异常 - 点击手动检查';
                break;
            case 'offline':
                badgeClass = 'api-offline';
                badgeColor = 'bg-danger';
                title = 'API服务离线 - 点击手动检查';
                break;
            case 'checking':
                badgeClass = 'api-checking';
                badgeColor = 'bg-secondary';
                title = '正在检查API状态...';
                break;
            default:
                badgeClass = 'api-unknown';
                badgeColor = 'bg-warning';
                title = 'API状态未知 - 点击手动检查';
        }

        this.statusElement.innerHTML = `
            <span class="badge ${badgeColor} api-status-badge ${badgeClass}" title="${title}">
                <span class="status-indicator"></span>${message}
            </span>
        `;

        // 存储状态到localStorage
        localStorage.setItem('apiStatus', status);
        localStorage.setItem('apiStatusTime', Date.now().toString());
    }

    manualCheck() {
        const now = Date.now();
        if (now - this.lastManualCheck < this.manualCheckCooldown) {
            console.log('手动检查过于频繁，请等待...');
            return;
        }

        this.lastManualCheck = now;
        this.retryCount = 0;
        this.isChecking = false;
        this.checkStatus();
        
        console.log('执行手动API状态检查');
    }

    startPeriodicCheck() {
        // 防止重复启动定时器
        if (this.checkInterval) {
            console.log('API状态定时检查已在运行，跳过重复启动');
            return;
        }

        // 使用动态间隔，根据连续失败次数调整
        const scheduleNextCheck = () => {
            if (!this.isChecking) {
                this.checkStatus();
            }

            // 根据状态调整下次检查间隔
            let nextInterval = this.checkIntervalMs;
            if (this.consecutiveFailures > 0) {
                nextInterval = Math.min(this.checkIntervalMs * Math.pow(1.2, this.consecutiveFailures), 120000); // 最大2分钟
            }

            this.checkInterval = setTimeout(scheduleNextCheck, nextInterval);
        };

        this.checkInterval = setTimeout(scheduleNextCheck, this.checkIntervalMs);
        console.log(`API状态定时检查已启动，初始间隔: ${this.checkIntervalMs}ms`);
    }

    stopPeriodicCheck() {
        if (this.checkInterval) {
            clearTimeout(this.checkInterval);
            this.checkInterval = null;
            console.log('API状态定时检查已停止');
        }
    }

    handleVisibilityChange() {
        if (!document.hidden) {
            // 页面变为可见时，立即检查状态
            console.log('页面变为可见，立即检查API状态');
            this.checkStatus();
        }
    }

    // 从localStorage恢复状态
    restoreStatus() {
        const savedStatus = localStorage.getItem('apiStatus');
        const savedTime = localStorage.getItem('apiStatusTime');
        
        if (savedStatus && savedTime) {
            const age = Date.now() - parseInt(savedTime);
            const maxAge = 5 * 60 * 1000; // 5分钟有效期
            
            if (age < maxAge) {
                const statusMap = {
                    'online': '在线',
                    'offline': '离线',
                    'checking': '检测中...'
                };
                
                this.updateUI(savedStatus, statusMap[savedStatus] || '未知');
                console.log(`从缓存恢复API状态: ${savedStatus}`);
                return true;
            }
        }
        return false;
    }
}

// 创建全局实例（防止重复创建）
if (!window.apiStatusManager) {
    window.apiStatusManager = new ApiStatusManager();
}

// 当DOM加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待一小段时间确保其他脚本加载完成
    setTimeout(() => {
        window.apiStatusManager.init();
        
        // 尝试恢复上次状态
        if (!window.apiStatusManager.restoreStatus()) {
            // 如果没有有效缓存，立即检查
            window.apiStatusManager.checkStatus();
        }
    }, 500);
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (window.apiStatusManager) {
        window.apiStatusManager.handleVisibilityChange();
    }
});

// 在页面卸载时停止定时器
window.addEventListener('beforeunload', function() {
    if (window.apiStatusManager) {
        window.apiStatusManager.stopPeriodicCheck();
    }
});

// 导出API状态管理器
window.ApiStatusManager = ApiStatusManager;