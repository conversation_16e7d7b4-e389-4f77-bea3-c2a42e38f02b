# Flask应用配置
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your_secret_key_here

# API配置
API_KEY=lxw8025031

# 数据库配置
# 数据库类型: sqlite 或 postgresql
DATABASE_TYPE=postgresql
# PostgreSQL连接字符串
DATABASE_URI=*********************************************************/hdsc_serial_numbers_db
# SQLite备份路径（用于回滚）
SQLITE_BACKUP_PATH=serial_numbers.db.backup

# 日志配置
LOG_FILE_PATH=logs/server.log
LOG_LEVEL=INFO

# 时区配置
TZ=Asia/Shanghai

# 缓存配置
CACHE_TYPE=SimpleCache
CACHE_DEFAULT_TIMEOUT=300

# 会话配置
SESSION_LIFETIME_HOURS=8