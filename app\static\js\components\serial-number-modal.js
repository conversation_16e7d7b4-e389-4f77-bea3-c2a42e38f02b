/**
 * Serial Number Modal Component - 串码查重弹窗组件
 * 参照二维码组件的工作模式，统一管理触发、模态框初始化与显示/隐藏
 */

class SerialNumberModalModule {
    constructor(triggerElement) {
        this.triggerElement = triggerElement;
        this.modal = null;
        this.init();
    }

    init() {
        this.bindTrigger();
        this.setupModal();
    }

    bindTrigger() {
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }
    }

    setupModal() {
        this.modal = document.getElementById('serialNumberModal');
        if (!this.modal) return;

        // 对齐二维码组件的模态事件绑定，增强一致性与可用性
        this.modal.addEventListener('show.bs.modal', () => {
            // 可以在此处执行输入区状态重置等操作
            // 统一录入处理器会在其自身模块中维护输入与结果，无需重复
        });

        this.modal.addEventListener('shown.bs.modal', () => {
            // 聚焦到输入区域，提升可用性
            const input = this.modal.querySelector('#unifiedSerialNumberInput');
            if (input) input.focus();
        });

        this.modal.addEventListener('hide.bs.modal', () => {
            // 清理当前模态内的焦点，避免键盘事件残留
            const focused = document.activeElement;
            if (focused && this.modal.contains(focused)) {
                focused.blur();
            }
        });

        this.modal.addEventListener('hidden.bs.modal', () => {
            // 隐藏后可进行表单重置或状态恢复
            // 交由 UnifiedSerialNumberProcessor 自行管理结果显示与清空
            
            // 强制清理可能残留的遮罩层
            this.cleanupBackdrop();
        });

        // 关闭按钮额外处理（与二维码组件保持一致）
        const closeButtons = this.modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const focused = document.activeElement;
                if (focused && this.modal.contains(focused)) {
                    focused.blur();
                }
                if (this.triggerElement && document.activeElement === document.body) {
                    setTimeout(() => this.triggerElement.focus(), 100);
                }
                
                // 确保遮罩层被清理
                setTimeout(() => this.cleanupBackdrop(), 300);
            });
        });
    }

    // 模态框控制方法（与二维码组件一致）
    show() {
        if (!this.modal) return;
        const modal = new bootstrap.Modal(this.modal);
        modal.show();
    }

    hide() {
        if (!this.modal) return;
        const modal = bootstrap.Modal.getInstance(this.modal);
        if (modal) {
            modal.hide();
            // 延迟清理，确保Bootstrap完成动画
            setTimeout(() => this.cleanupBackdrop(), 200);
        }
    }

    // 清理遮罩层的方法
    cleanupBackdrop() {
        // 清理所有可能残留的modal-backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop) {
                backdrop.remove();
            }
        });
        
        // 确保body类被正确清理
        document.body.classList.remove('modal-open');
        
        // 清理body的style属性（Bootstrap可能设置padding-right）
        if (document.body.style.paddingRight) {
            document.body.style.paddingRight = '';
        }
        
        // 清理可能的overflow hidden样式
        if (document.body.style.overflow === 'hidden') {
            document.body.style.overflow = '';
        }
    }

    // 清理方法（目前与二维码组件一致的占位实现）
    destroy() {
        // 销毁前确保遮罩被清理
        this.cleanupBackdrop();
        
        if (this.triggerElement) {
            this.triggerElement.removeEventListener('click', this.show);
        }
        if (this.modal) {
            const events = ['show.bs.modal', 'shown.bs.modal', 'hide.bs.modal', 'hidden.bs.modal'];
            events.forEach(evt => this.modal.removeEventListener(evt, this[evt]));
            
            // 销毁Bootstrap模态框实例
            const modalInstance = bootstrap.Modal.getInstance(this.modal);
            if (modalInstance) {
                modalInstance.dispose();
            }
        }
    }
}

// 暴露到全局，供 HomePageController 检测与实例化
window.SerialNumberModalModule = SerialNumberModalModule;

// 全局遮罩清理监听器 - 防止任何情况下的遮罩残留
document.addEventListener('DOMContentLoaded', function() {
    // 监听所有模态框的隐藏事件
    document.addEventListener('hidden.bs.modal', function() {
        // 延迟清理，确保Bootstrap动画完成
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 0) {
                backdrops.forEach(backdrop => backdrop.remove());
                document.body.classList.remove('modal-open');
                if (document.body.style.paddingRight) {
                    document.body.style.paddingRight = '';
                }
                if (document.body.style.overflow === 'hidden') {
                    document.body.style.overflow = '';
                }
            }
        }, 100);
    });
});

